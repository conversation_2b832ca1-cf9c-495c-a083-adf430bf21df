#!/usr/bin/env python3
"""
Test script to verify the data handling fixes for 2D and 3D data.
This script tests:
1. 2D data: Multiple Y columns should be treated as signals within one measurement
2. 3D data: Multiple Z columns should be treated as signals within one dataset per file
"""

import sys
import os
import pandas as pd
from pathlib import Path

# Add the current directory to Python path to import our modules
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from ImportWizard import DataImportGUI

def test_2d_data_processing():
    """Test 2D data processing with multiple Y columns."""
    print("=" * 60)
    print("TESTING 2D DATA PROCESSING")
    print("=" * 60)

    # Create DataImportGUI instance
    wizard = DataImportGUI()
    
    # Load and parse the 2D test data
    test_file = "test_data/multi_2d_data.csv"
    if not os.path.exists(test_file):
        print(f"ERROR: Test file {test_file} not found!")
        return False
    
    try:
        # Parse the file
        wizard.parsed_data = pd.read_csv(test_file)
        print(f"Loaded 2D test data: {wizard.parsed_data.shape}")
        print(f"Columns: {list(wizard.parsed_data.columns)}")
        
        # Create assignments for 2D data with multiple Y columns
        assignments = {
            'x_column': 'X',
            'y_columns': ['Sin(X)', 'Cos(X)', 'Sin(2X)/2'],  # Multiple Y columns
            'multiple_measurements': False,  # Should combine into one measurement
            'x_name': 'X',
            'y_name': 'Y'
        }
        
        # Process the data
        result = wizard._process_2d_data(assignments)
        
        print(f"\nProcessed 2D data result type: {type(result)}")
        if isinstance(result, pd.DataFrame):
            print(f"Result shape: {result.shape}")
            print(f"Result columns: {list(result.columns)}")
            print("\nFirst few rows:")
            print(result.head())
            
            # Check if all Y columns are present as separate signals
            expected_y_cols = ['Sin(X)', 'Cos(X)', 'Sin(2X)/2']
            for y_col in expected_y_cols:
                if y_col in result.columns:
                    print(f"✓ Y signal '{y_col}' found in result")
                else:
                    print(f"✗ Y signal '{y_col}' MISSING from result")
                    return False
            
            print("\n✓ 2D data processing test PASSED")
            return True
        else:
            print(f"✗ Expected DataFrame, got {type(result)}")
            return False
            
    except Exception as e:
        print(f"✗ 2D data processing test FAILED: {e}")
        return False

def test_3d_data_processing():
    """Test 3D data processing with multiple Z columns."""
    print("\n" + "=" * 60)
    print("TESTING 3D DATA PROCESSING")
    print("=" * 60)
    
    # Create DataImportGUI instance
    wizard = DataImportGUI()
    
    # Load and parse the 3D test data
    test_file = "test_data/multi_3d_data.csv"
    if not os.path.exists(test_file):
        print(f"ERROR: Test file {test_file} not found!")
        return False
    
    try:
        # Parse the file
        wizard.parsed_data = pd.read_csv(test_file)
        print(f"Loaded 3D test data: {wizard.parsed_data.shape}")
        print(f"Columns: {list(wizard.parsed_data.columns)}")
        
        # Test flattened format with multiple Z columns
        print("\n--- Testing Flattened Format ---")
        assignments_flat = {
            'data_format': 'flattened',
            'x_column': 'X',
            'y_column': 'Y', 
            'z_columns': ['Ripple_Z', 'Gaussian_Z', 'Paraboloid_Z'],  # Multiple Z columns
            'x_name': 'X',
            'y_name': 'Y',
            'z_name': 'Z'
        }
        
        result_flat = wizard._process_3d_data(assignments_flat)
        
        print(f"Flattened result type: {type(result_flat)}")
        if isinstance(result_flat, pd.DataFrame):
            print(f"Result shape: {result_flat.shape}")
            print(f"Result columns: {list(result_flat.columns)}")
            print("\nFirst few rows:")
            print(result_flat.head())
            
            # Check if all Z columns are present as separate signals
            expected_z_cols = ['Ripple_Z', 'Gaussian_Z', 'Paraboloid_Z']
            for z_col in expected_z_cols:
                if z_col in result_flat.columns:
                    print(f"✓ Z signal '{z_col}' found in result")
                else:
                    print(f"✗ Z signal '{z_col}' MISSING from result")
                    return False
            
            print("✓ 3D flattened format test PASSED")
        else:
            print(f"✗ Expected DataFrame, got {type(result_flat)}")
            return False
        
        # Test array format (if applicable)
        print("\n--- Testing Array Format ---")
        # For array format, we need to set up different assignments
        # This is more complex and depends on the data structure
        # For now, we'll skip this test since our test data is in flattened format
        print("Array format test skipped (test data is in flattened format)")
        
        return True
            
    except Exception as e:
        print(f"✗ 3D data processing test FAILED: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests."""
    print("Testing Data Handling Fixes")
    print("Testing that:")
    print("- 2D data: Multiple Y columns become signals within one measurement")
    print("- 3D data: Multiple Z columns become signals within one dataset")
    
    # Run tests
    test_2d_passed = test_2d_data_processing()
    test_3d_passed = test_3d_data_processing()
    
    # Summary
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    print(f"2D Data Processing: {'PASSED' if test_2d_passed else 'FAILED'}")
    print(f"3D Data Processing: {'PASSED' if test_3d_passed else 'FAILED'}")
    
    if test_2d_passed and test_3d_passed:
        print("\n🎉 ALL TESTS PASSED! Data handling fixes are working correctly.")
        return True
    else:
        print("\n❌ SOME TESTS FAILED! Please check the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
