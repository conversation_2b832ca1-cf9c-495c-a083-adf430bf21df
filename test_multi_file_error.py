#!/usr/bin/env python3
"""
Test script to reproduce the 'list object has no to_csv' error
by simulating the multi-file workflow
"""

import pandas as pd
import numpy as np
import sys
import os
import traceback
import tempfile

# Add current directory to path
sys.path.insert(0, os.getcwd())

def create_test_data():
    """Create test data files for multi-file processing"""
    test_files = []
    
    # Create 3 test CSV files
    for i in range(3):
        # Create sample data
        x_data = np.linspace(0, 10, 50)
        y_data = np.sin(x_data + i) + np.random.normal(0, 0.1, 50)
        
        # Create DataFrame
        df = pd.DataFrame({
            'X': x_data,
            'Y': y_data
        })
        
        # Save to temporary file
        temp_file = tempfile.NamedTemporaryFile(mode='w', suffix=f'_test_{i}.csv', delete=False)
        df.to_csv(temp_file.name, index=False)
        test_files.append(temp_file.name)
        temp_file.close()
        
        print(f"Created test file: {temp_file.name}")
    
    return test_files

def test_import_wizard_processing():
    """Test the ImportWizard multi-file processing"""
    print("=== Testing ImportWizard Multi-File Processing ===")
    
    try:
        from ImportWizard import DataImportGUI
        import tkinter as tk

        # Create test files
        test_files = create_test_data()

        # Create a minimal tkinter root for ImportWizard
        root = tk.Tk()
        root.withdraw()  # Hide the main window

        # Create ImportWizard instance
        wizard = DataImportGUI(root)
        
        # Simulate loading multiple files
        wizard.file_paths = test_files
        
        # Set up basic parsing parameters
        wizard.header_row = 0
        wizard.data_start_row = 1
        
        # Parse the first file to get structure
        wizard.parse_file(test_files[0])
        
        # Set up column assignments for 2D data
        assignments = {
            'x_column': 'X',
            'y_columns': ['Y'],
            'x_name': 'X',
            'y_name': 'Y',
            'multiple_measurements': False
        }
        
        # Test the multi-file processing
        print("Testing _process_multi_file_data...")
        result = wizard._process_multi_file_data(test_files, '2D', assignments)
        
        print(f"Multi-file processing result type: {type(result)}")
        if isinstance(result, list):
            print(f"Result is list with {len(result)} items")
            for i, item in enumerate(result):
                print(f"  Item {i}: type={type(item)}")
                if hasattr(item, 'shape'):
                    print(f"    Shape: {item.shape}")
                if hasattr(item, 'columns'):
                    print(f"    Columns: {list(item.columns)}")
        else:
            print(f"Result is single item: {type(result)}")
            if hasattr(result, 'shape'):
                print(f"  Shape: {result.shape}")
            if hasattr(result, 'columns'):
                print(f"  Columns: {list(result.columns)}")
        
        # Clean up
        root.destroy()
        for file_path in test_files:
            os.unlink(file_path)
        
        return True
        
    except Exception as e:
        print(f"✗ ImportWizard test failed: {str(e)}")
        traceback.print_exc()
        return False

def test_plotting_app_creation():
    """Test creating PlottingApp2D with multi-file data"""
    print("\n=== Testing PlottingApp2D Creation ===")
    
    try:
        from PlottingApp2D import Plot2DWindow
        import tkinter as tk
        
        # Create test DataFrames (simulating ImportWizard output)
        datasets = []
        for i in range(3):
            x_data = np.linspace(0, 10, 50)
            y_data = np.sin(x_data + i) + np.random.normal(0, 0.1, 50)
            
            df = pd.DataFrame({
                'X': x_data,
                'Y': y_data
            })
            df.name = f"TestFile_{i}"
            datasets.append(df)
        
        print(f"Created {len(datasets)} test datasets")
        
        # Create config for multi-dataset 2D plotting
        config = {
            'multi_dataset': True,
            'title': 'Multi-File Test',
            'dimension': '2D'
        }
        
        # Create tkinter root
        root = tk.Tk()
        root.withdraw()
        
        # Create PlottingApp2D window
        plot_window = tk.Toplevel(root)
        app = Plot2DWindow(plot_window, datasets, config)
        
        print("✓ PlottingApp2D created successfully")
        print(f"  Measurements count: {len(app.measurements)}")
        
        # Test each measurement's to_dataframe method
        for i, measurement in enumerate(app.measurements):
            print(f"  Testing measurement {i}: {measurement.name}")
            df = measurement.to_dataframe()
            print(f"    to_dataframe() successful: {df.shape}")
            
            # Test to_csv on the DataFrame
            temp_file = tempfile.mktemp(suffix='.csv')
            df.to_csv(temp_file, index=False)
            print(f"    to_csv() successful")
            os.unlink(temp_file)
        
        # Clean up
        plot_window.destroy()
        root.destroy()
        
        return True
        
    except Exception as e:
        print(f"✗ PlottingApp2D test failed: {str(e)}")
        traceback.print_exc()
        return False

def test_save_functionality():
    """Test the save functionality that's causing the error"""
    print("\n=== Testing Save Functionality ===")
    
    try:
        from PlottingApp2D import Plot2DWindow
        import tkinter as tk
        
        # Create test DataFrame
        x_data = np.linspace(0, 10, 50)
        y_data = np.sin(x_data) + np.random.normal(0, 0.1, 50)
        
        df = pd.DataFrame({
            'X': x_data,
            'Y': y_data
        })
        df.name = "TestData"
        
        # Create config
        config = {
            'multi_dataset': False,
            'title': 'Save Test',
            'dimension': '2D'
        }
        
        # Create tkinter root
        root = tk.Tk()
        root.withdraw()
        
        # Create PlottingApp2D window
        plot_window = tk.Toplevel(root)
        app = Plot2DWindow(plot_window, df, config)
        
        print("✓ PlottingApp2D created for save test")
        print(f"  Measurements count: {len(app.measurements)}")
        
        # Test the save functionality by calling the internal save method
        # This should trigger the same code path as the save button
        
        # Create temporary directory for save test
        temp_dir = tempfile.mkdtemp()
        app.project_base_dir = temp_dir
        
        # Try to save (this might trigger the error)
        print("  Testing save functionality...")
        
        # We can't easily test the full save without GUI interaction,
        # but we can test the core save logic
        for measurement in app.measurements:
            print(f"    Testing measurement: {measurement.name}")
            df_result = measurement.to_dataframe()
            print(f"      to_dataframe() type: {type(df_result)}")
            print(f"      to_dataframe() is DataFrame: {isinstance(df_result, pd.DataFrame)}")
            
            # Test to_csv
            temp_file = tempfile.mktemp(suffix='.csv')
            df_result.to_csv(temp_file, index=False)
            print(f"      to_csv() successful")
            os.unlink(temp_file)
        
        # Clean up
        plot_window.destroy()
        root.destroy()
        
        return True
        
    except Exception as e:
        print(f"✗ Save functionality test failed: {str(e)}")
        traceback.print_exc()
        return False

def main():
    """Run all tests to identify the source of the error"""
    print("Testing Multi-File Workflow to Find 'list object has no to_csv' Error")
    print("=" * 70)
    
    test1 = test_import_wizard_processing()
    test2 = test_plotting_app_creation()
    test3 = test_save_functionality()
    
    print("\n" + "=" * 70)
    if test1 and test2 and test3:
        print("✓ All tests passed - no obvious source of to_csv error found")
        print("\nThe error might occur in a specific workflow combination.")
        print("Please run the full application and reproduce the error,")
        print("then check the detailed error message for more information.")
    else:
        print("✗ Found issues in testing - check output above")
    
    return 0

if __name__ == "__main__":
    exit(main())
