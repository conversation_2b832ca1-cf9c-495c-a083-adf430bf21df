import pandas as pd

# Test DataFrame name attribute
df = pd.DataFrame({'x': [1,2,3], 'y': [4,5,6]})
print(f"Initial DataFrame name: {getattr(df, 'name', 'No name attribute')}")
print(f"Has name attribute: {hasattr(df, 'name')}")

# Try to set name
try:
    df.name = 'test_name'
    print(f"After setting name: {df.name}")
    print(f"Name access works: {hasattr(df, 'name') and df.name == 'test_name'}")
except Exception as e:
    print(f"Error setting name: {e}")

# Test if we can access it later
try:
    name_value = df.name if hasattr(df, 'name') else 'No name'
    print(f"Name value: {name_value}")
except Exception as e:
    print(f"Error accessing name: {e}")
