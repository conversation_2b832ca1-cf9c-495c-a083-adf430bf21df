"""
2D Plotting Window - Adapted from PlottingApp.py
Recreates the original PlottingApp interface but works with data from ImportWizard
"""

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
import pandas as pd
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg, NavigationToolbar2Tk
import numpy as np
import json
import os
from pathlib import Path
import datetime


# Project management configuration
APP_CONFIG_FILE = os.path.join(os.path.expanduser("~"), ".plottingapp_config.json")
DEFAULT_PROJECT_DIR = os.path.join(os.path.expanduser("~"), "Documents", "PlottingApp_Projects")

def get_project_directory():
    """Get the project directory from config, with fallback to default"""
    try:
        if os.path.exists(APP_CONFIG_FILE):
            with open(APP_CONFIG_FILE, 'r') as f:
                config = json.load(f)
                return config.get('project_directory', DEFAULT_PROJECT_DIR)
    except Exception:
        pass
    return DEFAULT_PROJECT_DIR

def save_project_directory(directory):
    """Save the project directory to config file"""
    try:
        config = {}
        if os.path.exists(APP_CONFIG_FILE):
            with open(APP_CONFIG_FILE, 'r') as f:
                config = json.load(f)

        config['project_directory'] = directory

        with open(APP_CONFIG_FILE, 'w') as f:
            json.dump(config, f, indent=4)
        return True
    except Exception as e:
        print(f"Failed to save config: {e}")
        return False

def check_directory_permissions(directory):
    """Check if we have read/write permissions for the directory"""
    try:
        # Create directory if it doesn't exist
        os.makedirs(directory, exist_ok=True)

        # Test write permission by creating a temporary file
        test_file = os.path.join(directory, '.permission_test')
        with open(test_file, 'w') as f:
            f.write('test')

        # Test read permission
        with open(test_file, 'r') as f:
            f.read()

        # Clean up test file
        os.remove(test_file)
        return True

    except (OSError, PermissionError, IOError):
        return False

def setup_project_directory():
    """Setup and validate project directory on app launch"""
    current_dir = get_project_directory()

    # Check if current directory is accessible
    if check_directory_permissions(current_dir):
        return current_dir

    # If not accessible, prompt user for new directory
    from tkinter import filedialog

    messagebox.showwarning(
        "Directory Access Issue",
        f"Cannot access project directory:\n{current_dir}\n\n"
        "Please select a new directory where you have read/write permissions."
    )

    while True:
        new_dir = filedialog.askdirectory(
            title="Select Project Directory",
            initialdir=os.path.expanduser("~")
        )

        if not new_dir:  # User cancelled
            messagebox.showerror(
                "Directory Required",
                "A project directory is required for the application to function.\n"
                "The application will now exit."
            )
            return None

        # Test the selected directory
        if check_directory_permissions(new_dir):
            # Save the new directory to config
            if save_project_directory(new_dir):
                messagebox.showinfo(
                    "Directory Set",
                    f"Project directory set to:\n{new_dir}"
                )
                return new_dir
            else:
                messagebox.showerror(
                    "Config Save Failed",
                    "Failed to save directory configuration.\n"
                    "Please try again."
                )
        else:
            messagebox.showerror(
                "Permission Denied",
                f"Cannot write to selected directory:\n{new_dir}\n\n"
                "Please select a directory where you have read/write permissions."
            )

# Style mappings from original PlottingApp
marker_map = {
    "None": "", "Circle": "o", "Square": "s", "Triangle": "^",
    "Diamond": "D", "Plus": "+", "Cross": "x", "Star": "*"
}

linestyle_map = {
    "Solid": "-", "Dashed": "--", "Dotted": ":", "Dash-dot": "-."
}

class Measurement:
    """Data structure for 2D measurements - compatible with original PlottingApp"""
    def __init__(self, name, x, y_dict, dimension=2):
        self.name = name
        # Ensure x is numpy array
        self.x = np.array(x) if isinstance(x, list) else x
        self.y_signals = {}
        self.dimension = dimension

        # Convert y_dict to the expected format
        for signal_name, y_data in y_dict.items():
            # Ensure y_data is numpy array
            if isinstance(y_data, list):
                y_data = np.array(y_data)
            self.y_signals[signal_name] = {
                "data": y_data,
                "enabled": True,
                "y2": False
            }

    def add_y_signal(self, signal_name, y_data):
        """Add a Y signal to the measurement"""
        # Ensure y_data is numpy array
        if isinstance(y_data, list):
            y_data = np.array(y_data)
        self.y_signals[signal_name] = {
            "data": y_data,
            "enabled": True,
            "y2": False
        }

    def to_dataframe(self):
        """Convert measurement to DataFrame"""
        try:
            # Validate that y_signals is properly structured
            if not isinstance(self.y_signals, dict):
                raise ValueError(f"y_signals must be a dict, got {type(self.y_signals)}")

            # Find the minimum length among all arrays to ensure consistent sizing
            min_length = len(self.x)
            for signal_name, signal_info in self.y_signals.items():
                # Ensure signal_info is a dict with 'data' key
                if not isinstance(signal_info, dict) or "data" not in signal_info:
                    raise ValueError(f"Signal '{signal_name}' info must be a dict with 'data' key, got {type(signal_info)}")

                # Ensure signal_info["data"] is not a list
                if isinstance(signal_info["data"], list):
                    signal_info["data"] = np.array(signal_info["data"])
                signal_length = len(signal_info["data"])
                min_length = min(min_length, signal_length)

            # Create DataFrame with truncated arrays to ensure same size
            df_dict = {"x": self.x[:min_length]}
            for signal_name, signal_info in self.y_signals.items():
                # Ensure data is numpy array before slicing
                data = signal_info["data"]
                if isinstance(data, list):
                    data = np.array(data)
                df_dict[signal_name] = data[:min_length]

            # Create and return DataFrame
            result = pd.DataFrame(df_dict)
            return result

        except Exception as e:
            # If there's an error, provide detailed information
            error_msg = f"Error in to_dataframe() for measurement '{self.name}': {e}"
            error_msg += f"\nMeasurement details:"
            error_msg += f"\n  x type: {type(self.x)}, length: {len(self.x) if hasattr(self.x, '__len__') else 'no len'}"
            error_msg += f"\n  y_signals type: {type(self.y_signals)}"
            if isinstance(self.y_signals, dict):
                error_msg += f"\n  y_signals keys: {list(self.y_signals.keys())}"
                for key, value in self.y_signals.items():
                    error_msg += f"\n    {key}: {type(value)}"
            raise RuntimeError(error_msg) from e

class Plot2DWindow:
    """2D Plotting Window - Recreated from PlottingApp.py"""

    def __init__(self, master, data, config, project_callback=None):
        self.master = master
        self.config = config
        self.project_callback = project_callback
        self.measurements = []
        self.lines = {}
        self.measurement_items = {}
        self.y2_state = {}
        self.signal_check_vars = {}
        self.signal_y2_vars = {}

        # Multi-dataset support for 2D (display all datasets in one plot)
        self.multi_dataset = config.get('multi_dataset', False)
        if self.multi_dataset and isinstance(data, list):
            self.datasets = data
            self.data = data[0] if data else None  # Keep first dataset as primary
        else:
            self.datasets = [data] if data is not None else []
            self.data = data

        # Setup project directory from config or fallback to setup
        if 'project_directory' in self.config:
            self.project_base_dir = self.config['project_directory']
            # Verify the directory is accessible
            if not check_directory_permissions(self.project_base_dir):
                messagebox.showwarning(
                    "Project Directory Access",
                    f"Cannot access project directory:\n{self.project_base_dir}\n\n"
                    "Please check the directory permissions in the main application settings."
                )
                # Use a fallback directory for this session
                self.project_base_dir = DEFAULT_PROJECT_DIR
        else:
            # Fallback to original setup if no config provided
            self.project_base_dir = setup_project_directory()
            if self.project_base_dir is None:
                # User cancelled directory selection, close the window
                self.master.destroy()
                return

        self.master.title("2D Plot Window")
        self.master.geometry("1400x800")

        # Make window resizable
        self.master.resizable(True, True)

        # Initialize plotting variables
        self.ax = None
        self.ax2 = None
        self.fig = None
        self.canvas = None

        self.init_plot_ui()
        self.load_data_from_import()
        self.update_y2_state()  # Initialize Y2 controls state
        self.draw_plot()

    def init_plot_ui(self):
        """Initialize the plotting UI with resizable layout"""
        self.frame = ttk.Frame(self.master)
        self.frame.pack(fill="both", expand=True)

        # Create main paned window for resizable layout
        main_paned = ttk.PanedWindow(self.frame, orient="horizontal")
        main_paned.pack(fill="both", expand=True, padx=5, pady=5)

        # Left frame for controls (resizable)
        left_frame = ttk.Frame(main_paned)
        main_paned.add(left_frame, weight=1)

        # Add action buttons at the top of left frame
        button_frame = ttk.Frame(left_frame)
        button_frame.pack(fill="x", padx=5, pady=5)

        ttk.Button(button_frame, text="Update Plot", command=self.draw_plot).pack(side="top", fill="x", pady=2)
        ttk.Button(button_frame, text="Save Project", command=self.export_project).pack(side="top", fill="x", pady=2)
        
        # Measurement tree
        treeview_frame = ttk.Frame(left_frame)
        treeview_frame.pack(fill="both", expand=True)

        # Add instruction label
        instruction_label = ttk.Label(treeview_frame,
                                    text="💡 Double-click measurement names to rename",
                                    font=("Arial", 9),
                                    foreground="gray")
        instruction_label.pack(anchor="w", pady=(0, 5))

        self.measurement_tree = ttk.Treeview(
            treeview_frame,
            columns=('enabled', 'y2'),
            show='tree headings',
            selectmode='browse'
        )
        self.measurement_tree.heading('#0', text='Measurement / Signal')
        self.measurement_tree.heading('enabled', text='Plot', command=lambda: self.toggle_all_signals())
        self.measurement_tree.heading('y2', text='Y2', command=lambda: self.toggle_all_y2())
        self.measurement_tree.column('#0', width=160, anchor='w')
        self.measurement_tree.column('enabled', width=50, anchor='center')
        self.measurement_tree.column('y2', width=30, anchor='center')
        self.measurement_tree.pack(side="left", fill="both", expand=True)

        # Scrollbar for tree
        scrollbar = ttk.Scrollbar(treeview_frame, orient="vertical", command=self.measurement_tree.yview)
        scrollbar.pack(side="right", fill="y")
        self.measurement_tree.configure(yscrollcommand=scrollbar.set)

        # Bind tree click events
        self.measurement_tree.bind('<ButtonRelease-1>', self.handle_tree_click)
        self.measurement_tree.bind('<Double-1>', self.handle_tree_double_click)

        # Right frame for plot and controls (resizable)
        right_frame = ttk.Frame(main_paned)
        main_paned.add(right_frame, weight=3)  # Give more weight to plot area

        # Create vertical paned window for plot and controls
        plot_paned = ttk.PanedWindow(right_frame, orient="vertical")
        plot_paned.pack(fill="both", expand=True)

        # Plot frame (resizable)
        plot_frame = ttk.Frame(plot_paned)
        plot_paned.add(plot_frame, weight=3)

        # Create matplotlib figure
        self.fig, self.ax = plt.subplots(figsize=(10, 6))
        self.ax2 = None
        self.canvas = FigureCanvasTkAgg(self.fig, master=plot_frame)
        self.canvas.get_tk_widget().pack(fill="both", expand=True)

        # Navigation toolbar
        toolbar = NavigationToolbar2Tk(self.canvas, plot_frame)
        toolbar.update()
        self.canvas._tkcanvas.pack(fill="both", expand=True)

        # Controls frame (resizable)
        controls_frame = ttk.Frame(plot_paned)
        plot_paned.add(controls_frame, weight=1)

        # Create tabbed interface for controls
        self.notebook = ttk.Notebook(controls_frame)
        self.notebook.pack(fill="both", expand=True, pady=5)

        # Plotting tab
        self.plotting_tab = ttk.Frame(self.notebook)
        self.notebook.add(self.plotting_tab, text="Plotting")

        # Editing tab
        self.editing_tab = ttk.Frame(self.notebook)
        self.notebook.add(self.editing_tab, text="Editing")

        # Setup control panels in plotting tab
        self.setup_control_panels(self.plotting_tab)

        # Setup editing controls in editing tab
        self.setup_editing_controls(self.editing_tab)

    def setup_control_panels(self, parent):
        """Setup all control panels with scrollable interface"""
        # Create main container with scrollable frame
        main_container = ttk.Frame(parent)
        main_container.pack(fill="both", expand=True, padx=5, pady=5)

        # Create canvas and scrollbar for scrollable controls
        canvas = tk.Canvas(main_container)
        scrollbar = ttk.Scrollbar(main_container, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # Pack canvas and scrollbar
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Bind mousewheel to canvas
        def _on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        canvas.bind_all("<MouseWheel>", _on_mousewheel)

        # Font Style Frame
        self.font_frame = ttk.LabelFrame(scrollable_frame, text="Font Style")
        self.font_frame.pack(fill="x", padx=5, pady=3)

        # Plot Style Frame
        self.plot_style_frame = ttk.LabelFrame(scrollable_frame, text="Plot Style")
        self.plot_style_frame.pack(fill="x", padx=5, pady=3)

        # X Axis Frame
        self.xaxis_frame = ttk.LabelFrame(scrollable_frame, text="X Axis Controls")
        self.xaxis_frame.pack(fill="x", padx=5, pady=3)

        # Y1 Axis Frame (separate from Y2)
        self.y1axis_frame = ttk.LabelFrame(scrollable_frame, text="Y1 Axis Controls")
        self.y1axis_frame.pack(fill="x", padx=5, pady=3)

        # Y2 Axis Frame (separate from Y1)
        self.y2axis_frame = ttk.LabelFrame(scrollable_frame, text="Y2 Axis Controls")
        self.y2axis_frame.pack(fill="x", padx=5, pady=3)



        # Initialize control variables
        self.init_control_variables()

        # Setup individual control panels
        self.setup_font_controls()
        self.setup_plot_style_controls()
        self.setup_axis_controls()

    def init_control_variables(self):
        """Initialize all control variables"""
        # Axis variables
        self.log_x_var = tk.BooleanVar(value=False)
        self.log_y_var = tk.BooleanVar(value=False)
        self.log_y2_var = tk.BooleanVar(value=False)

        # Style variables
        self.grid_var = tk.BooleanVar(value=True)
        self.legend_pos_var = tk.StringVar(value="best")
        self.label_mode_var = tk.StringVar(value="both")

        # Font variables
        self.font_var = tk.StringVar(value="Arial")
        self.fontsize_var = tk.StringVar(value="12")
        self.ticksize_var = tk.StringVar(value="10")

        # New customization variables
        self.title_size_var = tk.StringVar(value="14")
        self.legend_size_var = tk.StringVar(value="10")
        self.x_tick_spacing_var = tk.StringVar(value="auto")
        self.y_tick_spacing_var = tk.StringVar(value="auto")
        self.y2_tick_spacing_var = tk.StringVar(value="auto")

        # Individual opposite tick options for each axis
        self.x_opposite_ticks_var = tk.BooleanVar(value=False)
        self.y_opposite_ticks_var = tk.BooleanVar(value=False)
        self.y2_opposite_ticks_var = tk.BooleanVar(value=False)

        self.x_scientific_notation_var = tk.BooleanVar(value=False)
        self.y_scientific_notation_var = tk.BooleanVar(value=False)
        self.y2_scientific_notation_var = tk.BooleanVar(value=False)

        # Plot style variables
        self.marker_var = tk.StringVar(value="None")
        self.linestyle_var = tk.StringVar(value="Solid")
        self.linewidth_var = tk.StringVar(value="2.0")
        self.color_palette_var = tk.StringVar(value="default")

    def load_data_from_import(self):
        """Load data from ImportWizard format and convert to measurements"""
        if self.data is None or (hasattr(self.data, 'empty') and self.data.empty):
            return

        # Check if we have multiple files to load (legacy support)
        exported_files = self.config.get('exported_files', [])
        export_directory = self.config.get('export_directory', '')

        if len(exported_files) > 1 and export_directory:
            # Load multiple files (legacy behavior)
            self.load_multiple_files(exported_files, export_directory)
        else:
            # Load data directly from ImportWizard (new behavior)
            self.load_direct_data()

        # Apply plot configuration if available
        self.apply_plot_config()

        # Populate measurement tree
        self.populate_measurement_tree()

    def load_direct_data(self):
        """Load data directly from ImportWizard (supports multiple datasets for 2D)"""
        # Initialize measurements list
        self.measurements = []

        # Handle multiple datasets for 2D plotting (display all in one plot)
        if self.multi_dataset and len(self.datasets) > 1:
            print(f"Loading {len(self.datasets)} datasets for 2D multi-plot")
            self.load_multiple_datasets()
        else:
            # Single dataset
            if self.data is not None:
                self.load_single_dataset(self.data, self.config.get('title', 'Imported Data'))
            elif len(self.datasets) > 0:
                # Fallback: use first dataset if data is None but datasets exist
                self.load_single_dataset(self.datasets[0], self.config.get('title', 'Imported Data'))

    def load_multiple_datasets(self):
        """Load multiple 2D datasets and combine them into one measurement with all Y signals"""
        # For 2D data, multiple files should be combined into one measurement
        # Each file contributes its Y columns as separate signals

        combined_y_dict = {}
        x_data = None

        for i, dataset in enumerate(self.datasets):
            dataset_name = dataset.name if hasattr(dataset, 'name') else f"File_{i+1}"

            # Get numeric columns
            numeric_cols = dataset.select_dtypes(include=[np.number]).columns.tolist()

            if len(numeric_cols) >= 2:
                # Use first column as X (should be consistent across files)
                x_col = numeric_cols[0]
                y_cols = numeric_cols[1:]

                # Set X data from first file, verify consistency for others
                if x_data is None:
                    x_data = dataset[x_col].values
                else:
                    # For multi-file 2D, X should be consistent or we need to interpolate
                    # For now, use the first file's X data
                    pass

                # Add Y signals with file prefix to avoid name conflicts
                for y_col in y_cols:
                    signal_name = f"{dataset_name}_{y_col}"
                    y_values = dataset[y_col].values

                    # Ensure same length as X data
                    if len(y_values) == len(x_data):
                        combined_y_dict[signal_name] = y_values
                    else:
                        print(f"Warning: Skipping signal {signal_name} due to length mismatch")

            elif len(numeric_cols) == 1:
                # Single Y column - use index as X if no X data yet
                y_col = numeric_cols[0]
                if x_data is None:
                    x_data = np.arange(len(dataset))

                signal_name = f"{dataset_name}_{y_col}"
                y_values = dataset[y_col].values

                if len(y_values) == len(x_data):
                    combined_y_dict[signal_name] = y_values

        # Create single measurement with all Y signals
        if x_data is not None and combined_y_dict:
            # Filter out NaN values consistently across all signals
            valid_indices = ~np.isnan(x_data)
            for signal_name, y_values in combined_y_dict.items():
                valid_indices = valid_indices & ~np.isnan(y_values)

            # Apply filtering
            x_filtered = x_data[valid_indices]
            y_filtered = {}
            for signal_name, y_values in combined_y_dict.items():
                y_filtered[signal_name] = y_values[valid_indices]

            measurement_name = self.config.get('title', 'Combined 2D Data')
            measurement = Measurement(measurement_name, x_filtered, y_filtered)
            self.measurements.append(measurement)
        else:
            print("Warning: No valid data found in multiple datasets")

    def load_single_dataset(self, data, measurement_name):
        """Load a single dataset"""
        # Determine column assignments based on data structure
        numeric_cols = data.select_dtypes(include=[np.number]).columns.tolist()

        if len(numeric_cols) >= 2:
            # Use first column as X, rest as Y signals
            x_col = numeric_cols[0]
            y_cols = numeric_cols[1:]

            x_data = data[x_col].values
            y_dict = {}

            # Calculate global valid indices for all columns
            valid_indices = ~np.isnan(x_data)
            for y_col in y_cols:
                y_data = data[y_col].values
                # Combine valid indices (must be valid for both X and this Y)
                valid_indices = valid_indices & ~np.isnan(y_data)

            # Apply valid indices to all data consistently
            x_filtered = x_data[valid_indices]
            for y_col in y_cols:
                y_data = data[y_col].values
                y_dict[y_col] = y_data[valid_indices]

            # Create measurement with proper constructor arguments
            measurement = Measurement(measurement_name, x_filtered, y_dict)

        elif len(numeric_cols) == 1:
            # Single column: use index as X, column as Y
            y_col = numeric_cols[0]
            x_data = np.arange(len(data))
            y_data = data[y_col].values
            valid_indices = ~np.isnan(y_data)

            y_dict = {y_col: y_data[valid_indices]}
            measurement = Measurement(measurement_name, x_data[valid_indices], y_dict)
        else:
            # No numeric columns, create empty measurement
            measurement = Measurement(measurement_name, np.array([]), {})

        # Add measurement to the list (don't replace the entire list for multi-dataset)
        self.measurements.append(measurement)

    def apply_plot_config(self):
        """Apply plot configuration from ImportWizard if available"""
        plot_config = self.config.get('plot_config', {})

        if plot_config and isinstance(plot_config, dict):
            # Apply configuration using the existing load_config method
            try:
                self.load_config(plot_config)
            except Exception as e:
                print(f"Warning: Failed to apply plot config: {e}")

    def load_multiple_files(self, exported_files, export_directory):
        """Load multiple exported files as separate measurements"""
        for file_name in exported_files:
            try:
                file_path = os.path.join(export_directory, file_name)
                file_data = pd.read_csv(file_path, comment='#')

                if file_data.empty:
                    continue

                # Extract measurement name from filename (remove extension and dimension suffix)
                measurement_name = Path(file_name).stem
                # Remove dimension suffix if present (e.g., "_2D", "_3D")
                if measurement_name.endswith(('_1D', '_2D', '_3D')):
                    measurement_name = measurement_name[:-3]

                # Convert to Measurement object
                x_data = file_data.iloc[:, 0]
                y_dict = {}
                for col in file_data.columns[1:]:
                    y_dict[col] = file_data[col]

                measurement = Measurement(measurement_name, x_data, y_dict)
                self.measurements.append(measurement)

            except Exception as e:
                print(f"Warning: Failed to load file {file_name}: {e}")
                continue

    def load_single_file(self):
        """Load single file data (original behavior)"""
        # Convert ImportWizard data to Measurement objects
        # Assume first column is X, rest are Y signals
        x_data = self.data.iloc[:, 0]

        # Create y_dict from remaining columns
        y_dict = {}
        for col in self.data.columns[1:]:
            y_dict[col] = self.data[col]

        # Create measurement object
        measurement_name = getattr(self, 'measurement_name', 'Imported Data')
        measurement = Measurement(measurement_name, x_data, y_dict)
        self.measurements.append(measurement)

    def populate_measurement_tree(self):
        """Populate the measurement tree with data"""
        # Clear existing tree
        for item in self.measurement_tree.get_children():
            self.measurement_tree.delete(item)

        self.measurement_items.clear()
        self.signal_check_vars.clear()
        self.signal_y2_vars.clear()

        # Add measurements to tree
        for measurement in self.measurements:
            # Add measurement as parent item
            measurement_item = self.measurement_tree.insert('', 'end', text=measurement.name, values=('', ''))
            self.measurement_items[measurement_item] = measurement

            # Add signals as child items
            for signal_name, signal_info in measurement.y_signals.items():
                enabled_var = tk.BooleanVar(value=signal_info["enabled"])
                y2_var = tk.BooleanVar(value=signal_info["y2"])

                self.signal_check_vars[(measurement.name, signal_name)] = enabled_var
                self.signal_y2_vars[(measurement.name, signal_name)] = y2_var

                signal_item = self.measurement_tree.insert(
                    measurement_item, 'end',
                    text=signal_name,
                    values=(
                        '✅' if enabled_var.get() else '⬜️',
                        '✅' if y2_var.get() else '⬜️'
                    )
                )

    def setup_font_controls(self):
        """Setup font control panel"""
        # Title
        ttk.Label(self.font_frame, text="Figure Title:").pack(side="left", padx=(5, 0))
        self.title_entry = ttk.Entry(self.font_frame, width=15)
        self.title_entry.insert(0, self.config.get("title", ""))
        self.title_entry.pack(side="left", padx=(0, 5))

        # Font
        ttk.Label(self.font_frame, text="Font:").pack(side="left", padx=(5, 0))
        self.font_entry = ttk.Entry(self.font_frame, textvariable=self.font_var, width=8)
        self.font_entry.pack(side="left", padx=(0, 5))

        # Font size
        ttk.Label(self.font_frame, text="Font Size:").pack(side="left", padx=(5, 0))
        self.fontsize_entry = ttk.Entry(self.font_frame, textvariable=self.fontsize_var, width=5)
        self.fontsize_entry.pack(side="left", padx=(0, 5))

        # Title size
        ttk.Label(self.font_frame, text="Title Size:").pack(side="left", padx=(5, 0))
        self.title_size_entry = ttk.Entry(self.font_frame, textvariable=self.title_size_var, width=5)
        self.title_size_entry.pack(side="left", padx=(0, 5))

        # Tick size
        ttk.Label(self.font_frame, text="Tick Size:").pack(side="left", padx=(5, 0))
        self.ticksize_entry = ttk.Entry(self.font_frame, textvariable=self.ticksize_var, width=5)
        self.ticksize_entry.pack(side="left", padx=(0, 5))

        # Legend size
        ttk.Label(self.font_frame, text="Legend Size:").pack(side="left", padx=(5, 0))
        self.legend_size_entry = ttk.Entry(self.font_frame, textvariable=self.legend_size_var, width=5)
        self.legend_size_entry.pack(side="left", padx=(0, 5))

    def setup_plot_style_controls(self):
        """Setup plot style control panel"""
        # Grid
        ttk.Checkbutton(self.plot_style_frame, text="Grid", variable=self.grid_var).pack(side="left", padx=5)

        # Legend position
        ttk.Label(self.plot_style_frame, text="Legend:").pack(side="left", padx=(5, 0))
        legend_combo = ttk.Combobox(self.plot_style_frame, textvariable=self.legend_pos_var,
                                   values=["best", "upper right", "upper left", "lower left", "lower right",
                                          "right", "center left", "center right", "lower center", "upper center"],
                                   state="readonly", width=12)
        legend_combo.pack(side="left", padx=(0, 5))

        # Marker
        ttk.Label(self.plot_style_frame, text="Marker:").pack(side="left", padx=(5, 0))
        marker_combo = ttk.Combobox(self.plot_style_frame, textvariable=self.marker_var,
                                   values=list(marker_map.keys()), state="readonly", width=8)
        marker_combo.pack(side="left", padx=(0, 5))

        # Line style
        ttk.Label(self.plot_style_frame, text="Line Style:").pack(side="left", padx=(5, 0))
        linestyle_combo = ttk.Combobox(self.plot_style_frame, textvariable=self.linestyle_var,
                                      values=list(linestyle_map.keys()), state="readonly", width=8)
        linestyle_combo.pack(side="left", padx=(0, 5))

        # Line width
        ttk.Label(self.plot_style_frame, text="Line Width:").pack(side="left", padx=(5, 0))
        self.linewidth_entry = ttk.Entry(self.plot_style_frame, textvariable=self.linewidth_var, width=5)
        self.linewidth_entry.pack(side="left", padx=(0, 5))

        # Color palette
        ttk.Label(self.plot_style_frame, text="Colors:").pack(side="left", padx=(5, 0))
        color_combo = ttk.Combobox(self.plot_style_frame, textvariable=self.color_palette_var,
                                  values=["default", "tab10", "Set1", "Set2", "Set3", "Pastel1", "Pastel2"],
                                  state="readonly", width=8)
        color_combo.pack(side="left", padx=(0, 5))

        # Label mode
        ttk.Label(self.plot_style_frame, text="Labels:").pack(side="left", padx=(5, 0))
        label_mode_combo = ttk.Combobox(self.plot_style_frame, textvariable=self.label_mode_var,
                                       values=["file", "header", "both"], state="readonly", width=8)
        label_mode_combo.pack(side="left", padx=(0, 5))
        label_mode_combo.bind('<<ComboboxSelected>>', lambda e: self.draw_plot())

    def setup_axis_controls(self):
        """Setup separate axis control panels for X, Y1, and Y2"""
        # X Axis controls
        self.setup_x_axis_controls()
        self.setup_y1_axis_controls()
        self.setup_y2_axis_controls()

    def setup_x_axis_controls(self):
        """Setup X axis controls with tick spacing and scientific notation"""
        # Create sub-frames for organization
        basic_frame = ttk.Frame(self.xaxis_frame)
        basic_frame.pack(fill="x", pady=2)

        advanced_frame = ttk.Frame(self.xaxis_frame)
        advanced_frame.pack(fill="x", pady=2)

        # Basic X axis controls
        ttk.Label(basic_frame, text="X Label:").pack(side="left", padx=(5, 0))
        self.x_label_entry = ttk.Entry(basic_frame, width=12)
        self.x_label_entry.insert(0, self.config.get("x_label", "X Axis"))
        self.x_label_entry.pack(side="left", padx=(0, 5))

        ttk.Label(basic_frame, text="X min:").pack(side="left", padx=(5, 0))
        self.lower_x_lim_entry = ttk.Entry(basic_frame, width=8)
        self.lower_x_lim_entry.pack(side="left", padx=(0, 5))

        ttk.Label(basic_frame, text="X max:").pack(side="left", padx=(5, 0))
        self.upper_x_lim_entry = ttk.Entry(basic_frame, width=8)
        self.upper_x_lim_entry.pack(side="left", padx=(0, 5))

        ttk.Checkbutton(basic_frame, text="Log X", variable=self.log_x_var).pack(side="left", padx=5)

        # Advanced X axis controls
        ttk.Label(advanced_frame, text="Tick Spacing:").pack(side="left", padx=(5, 0))
        self.x_tick_spacing_entry = ttk.Entry(advanced_frame, textvariable=self.x_tick_spacing_var, width=8)
        self.x_tick_spacing_entry.pack(side="left", padx=(0, 2))
        ttk.Label(advanced_frame, text="(auto/number)", font=("Arial", 8)).pack(side="left", padx=(0, 5))

        ttk.Checkbutton(advanced_frame, text="Scientific Notation (x10^n)",
                       variable=self.x_scientific_notation_var).pack(side="left", padx=10)

        ttk.Checkbutton(advanced_frame, text="Show Opposite Ticks",
                       variable=self.x_opposite_ticks_var).pack(side="left", padx=10)

    def setup_y1_axis_controls(self):
        """Setup Y1 axis controls with tick spacing and scientific notation"""
        # Create sub-frames for organization
        basic_frame = ttk.Frame(self.y1axis_frame)
        basic_frame.pack(fill="x", pady=2)

        advanced_frame = ttk.Frame(self.y1axis_frame)
        advanced_frame.pack(fill="x", pady=2)

        # Basic Y1 axis controls
        ttk.Label(basic_frame, text="Y1 Label:").pack(side="left", padx=(5, 0))
        self.y1_label_entry = ttk.Entry(basic_frame, width=12)
        self.y1_label_entry.insert(0, self.config.get("y1_label", "Y1 Axis"))
        self.y1_label_entry.pack(side="left", padx=(0, 5))

        ttk.Label(basic_frame, text="Y1 min:").pack(side="left", padx=(5, 0))
        self.lower_y1_lim_entry = ttk.Entry(basic_frame, width=8)
        self.lower_y1_lim_entry.pack(side="left", padx=(0, 5))

        ttk.Label(basic_frame, text="Y1 max:").pack(side="left", padx=(5, 0))
        self.upper_y1_lim_entry = ttk.Entry(basic_frame, width=8)
        self.upper_y1_lim_entry.pack(side="left", padx=(0, 5))

        ttk.Checkbutton(basic_frame, text="Log Y1", variable=self.log_y_var).pack(side="left", padx=5)

        # Advanced Y1 axis controls
        ttk.Label(advanced_frame, text="Tick Spacing:").pack(side="left", padx=(5, 0))
        self.y_tick_spacing_entry = ttk.Entry(advanced_frame, textvariable=self.y_tick_spacing_var, width=8)
        self.y_tick_spacing_entry.pack(side="left", padx=(0, 2))
        ttk.Label(advanced_frame, text="(auto/number)", font=("Arial", 8)).pack(side="left", padx=(0, 5))

        ttk.Checkbutton(advanced_frame, text="Scientific Notation (x10^n)",
                       variable=self.y_scientific_notation_var).pack(side="left", padx=10)

        ttk.Checkbutton(advanced_frame, text="Show Opposite Ticks",
                       variable=self.y_opposite_ticks_var).pack(side="left", padx=10)

    def setup_y2_axis_controls(self):
        """Setup Y2 axis controls with tick spacing and scientific notation"""
        # Create sub-frames for organization
        basic_frame = ttk.Frame(self.y2axis_frame)
        basic_frame.pack(fill="x", pady=2)

        advanced_frame = ttk.Frame(self.y2axis_frame)
        advanced_frame.pack(fill="x", pady=2)

        # Basic Y2 axis controls
        ttk.Label(basic_frame, text="Y2 Label:").pack(side="left", padx=(5, 0))
        self.y2_label_entry = ttk.Entry(basic_frame, width=12)
        self.y2_label_entry.insert(0, self.config.get("y2_label", "Y2 Axis"))
        self.y2_label_entry.pack(side="left", padx=(0, 5))

        ttk.Label(basic_frame, text="Y2 min:").pack(side="left", padx=(5, 0))
        self.lower_y2_lim_entry = ttk.Entry(basic_frame, width=8)
        self.lower_y2_lim_entry.pack(side="left", padx=(0, 5))

        ttk.Label(basic_frame, text="Y2 max:").pack(side="left", padx=(5, 0))
        self.upper_y2_lim_entry = ttk.Entry(basic_frame, width=8)
        self.upper_y2_lim_entry.pack(side="left", padx=(0, 5))

        self.log_y2_cb = ttk.Checkbutton(basic_frame, text="Log Y2", variable=self.log_y2_var)
        self.log_y2_cb.pack(side="left", padx=5)

        # Advanced Y2 axis controls
        ttk.Label(advanced_frame, text="Tick Spacing:").pack(side="left", padx=(5, 0))
        self.y2_tick_spacing_entry = ttk.Entry(advanced_frame, textvariable=self.y2_tick_spacing_var, width=8)
        self.y2_tick_spacing_entry.pack(side="left", padx=(0, 2))
        ttk.Label(advanced_frame, text="(auto/number)", font=("Arial", 8)).pack(side="left", padx=(0, 5))

        self.y2_scientific_cb = ttk.Checkbutton(advanced_frame, text="Scientific Notation (x10^n)",
                                               variable=self.y2_scientific_notation_var)
        self.y2_scientific_cb.pack(side="left", padx=10)

        self.y2_opposite_ticks_cb = ttk.Checkbutton(advanced_frame, text="Show Opposite Ticks",
                                                  variable=self.y2_opposite_ticks_var)
        self.y2_opposite_ticks_cb.pack(side="left", padx=10)



    def update_y2_controls_state(self):
        """Enable/disable Y2 controls based on whether Y2 axis is being used"""
        y2_in_use = False

        # Check if any signal is assigned to Y2 axis
        for measurement in self.measurements:
            for signal_name in measurement.y_signals:
                if (measurement.name, signal_name) in self.signal_y2_vars:
                    if self.signal_y2_vars[(measurement.name, signal_name)].get():
                        y2_in_use = True
                        break
            if y2_in_use:
                break

        # Enable/disable Y2 controls based on usage
        state = "normal" if y2_in_use else "disabled"

        # Y2 axis controls
        for widget in self.y2axis_frame.winfo_children():
            if hasattr(widget, 'configure'):
                try:
                    widget.configure(state=state)
                except tk.TclError:
                    # Some widgets don't support state configuration
                    pass
            # Also handle nested frames
            for child in widget.winfo_children():
                if hasattr(child, 'configure'):
                    try:
                        child.configure(state=state)
                    except tk.TclError:
                        pass

    def setup_editing_controls(self, parent):
        """Setup editing controls in the editing tab"""
        # Create scrollable frame for editing controls
        canvas = tk.Canvas(parent)
        scrollbar = ttk.Scrollbar(parent, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # Data Processing Frame
        processing_frame = ttk.LabelFrame(scrollable_frame, text="Data Processing")
        processing_frame.pack(fill="x", padx=5, pady=3)

        # Row 1: Basic operations
        row1 = ttk.Frame(processing_frame)
        row1.pack(fill="x", padx=5, pady=2)
        ttk.Button(row1, text="Normalize", command=self.normalize_data).pack(side="left", padx=2)
        ttk.Button(row1, text="Smooth", command=self.smooth_data).pack(side="left", padx=2)
        ttk.Button(row1, text="Crop", command=self.crop_data).pack(side="left", padx=2)
        ttk.Button(row1, text="Shift", command=self.shift_data).pack(side="left", padx=2)

        # Row 2: Advanced operations
        row2 = ttk.Frame(processing_frame)
        row2.pack(fill="x", padx=5, pady=2)
        ttk.Button(row2, text="FFT", command=self.apply_fft).pack(side="left", padx=2)
        ttk.Button(row2, text="Filter", command=self.filter_data).pack(side="left", padx=2)
        ttk.Button(row2, text="Baseline", command=self.baseline_correction).pack(side="left", padx=2)
        ttk.Button(row2, text="Deconvolute", command=self.deconvolute_data).pack(side="left", padx=2)

        # Data Analysis Frame
        analysis_frame = ttk.LabelFrame(scrollable_frame, text="Data Analysis")
        analysis_frame.pack(fill="x", padx=5, pady=3)

        # Row 3: Analysis operations
        row3 = ttk.Frame(analysis_frame)
        row3.pack(fill="x", padx=5, pady=2)
        ttk.Button(row3, text="Statistics", command=self.show_statistics).pack(side="left", padx=2)
        ttk.Button(row3, text="Peak Find", command=self.find_peaks).pack(side="left", padx=2)
        ttk.Button(row3, text="Integrate", command=self.integrate_data).pack(side="left", padx=2)
        ttk.Button(row3, text="Fit Curve", command=self.fit_curve).pack(side="left", padx=2)

        # Data Manipulation Frame
        manipulation_frame = ttk.LabelFrame(scrollable_frame, text="Data Manipulation")
        manipulation_frame.pack(fill="x", padx=5, pady=3)

        # Row 4: Manipulation operations
        row4 = ttk.Frame(manipulation_frame)
        row4.pack(fill="x", padx=5, pady=2)
        ttk.Button(row4, text="Subtract", command=self.subtract_data).pack(side="left", padx=2)
        ttk.Button(row4, text="Add", command=self.add_data).pack(side="left", padx=2)
        ttk.Button(row4, text="Multiply", command=self.multiply_data).pack(side="left", padx=2)
        ttk.Button(row4, text="Formula", command=self.custom_formula).pack(side="left", padx=2)

        # Data Export Frame
        export_frame = ttk.LabelFrame(scrollable_frame, text="Data Export")
        export_frame.pack(fill="x", padx=5, pady=3)

        # Row 5: Export operations
        row5 = ttk.Frame(export_frame)
        row5.pack(fill="x", padx=5, pady=2)
        ttk.Button(row5, text="Export Data", command=self.export_data).pack(side="left", padx=2)
        ttk.Button(row5, text="Export Plot", command=self.export_plot).pack(side="left", padx=2)

        # Pack canvas and scrollbar
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

    def handle_tree_click(self, event):
        """Handle clicks on the measurement tree"""
        region = self.measurement_tree.identify_region(event.x, event.y)
        column = self.measurement_tree.identify_column(event.x)
        row = self.measurement_tree.identify_row(event.y)

        if not row or column not in ('#1', '#2'):
            return

        item = self.measurement_tree.item(row)
        parent_id = self.measurement_tree.parent(row)

        if parent_id:  # Signal level
            measurement_name = self.measurement_tree.item(parent_id)['text']
            signal_label = item['text']

            if column == '#1':  # Toggle Enabled
                var = self.signal_check_vars.get((measurement_name, signal_label))
                if var:
                    var.set(not var.get())
                    self.measurement_tree.set(row, 'enabled', '✅' if var.get() else '⬜️')

            elif column == '#2':  # Toggle Y2
                var = self.signal_y2_vars.get((measurement_name, signal_label))
                if var:
                    var.set(not var.get())
                    self.measurement_tree.set(row, 'y2', '✅' if var.get() else '⬜️')

            self.update_y2_state()
            self.draw_plot()

    def handle_tree_double_click(self, event):
        """Handle double-click on measurement tree for renaming"""
        row = self.measurement_tree.identify_row(event.y)
        if not row:
            return

        item = self.measurement_tree.item(row)
        parent_id = self.measurement_tree.parent(row)

        # Only allow renaming of measurement names (parent items), not signals
        if not parent_id:  # This is a measurement (parent item)
            current_name = item['text']
            measurement = self.measurement_items.get(row)
            if measurement:
                self.show_measurement_rename_dialog(measurement, current_name)

    def show_measurement_rename_dialog(self, measurement, current_name):
        """Show dialog for renaming measurement"""
        dialog = tk.Toplevel(self.master)
        dialog.title("Rename Measurement")
        dialog.geometry("400x150")
        dialog.resizable(False, False)
        dialog.transient(self.master)
        dialog.grab_set()

        # Center the dialog
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (dialog.winfo_width() // 2)
        y = (dialog.winfo_screenheight() // 2) - (dialog.winfo_height() // 2)
        dialog.geometry(f"+{x}+{y}")

        # Main frame
        main_frame = ttk.Frame(dialog, padding="20")
        main_frame.pack(fill="both", expand=True)

        # Current name label
        ttk.Label(main_frame, text=f"Current name: {current_name}").pack(anchor="w", pady=(0, 10))

        # New name entry
        ttk.Label(main_frame, text="New name:").pack(anchor="w")
        name_var = tk.StringVar(value=current_name)
        name_entry = ttk.Entry(main_frame, textvariable=name_var, width=40)
        name_entry.pack(fill="x", pady=(5, 15))
        name_entry.select_range(0, tk.END)
        name_entry.focus()

        # Buttons frame
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill="x")

        def on_rename():
            new_name = name_var.get().strip()
            if not new_name:
                messagebox.showerror("Error", "Measurement name cannot be empty")
                return

            if new_name == current_name:
                dialog.destroy()
                return

            # Check for duplicate names
            existing_names = [m.name for m in self.measurements if m != measurement]
            if new_name in existing_names:
                messagebox.showerror("Error", f"A measurement named '{new_name}' already exists")
                return

            # Perform the rename
            self.perform_measurement_rename(measurement, new_name)
            dialog.destroy()

        def on_cancel():
            dialog.destroy()

        ttk.Button(button_frame, text="Rename", command=on_rename).pack(side="right", padx=(5, 0))
        ttk.Button(button_frame, text="Cancel", command=on_cancel).pack(side="right")

        # Bind Enter key to rename
        dialog.bind('<Return>', lambda e: on_rename())
        dialog.bind('<Escape>', lambda e: on_cancel())

    def perform_measurement_rename(self, measurement, new_name):
        """Perform the actual measurement rename"""
        old_name = measurement.name

        # Update measurement name
        measurement.name = new_name

        # Update signal check vars and y2 vars with new measurement name
        old_signal_vars = {}
        old_y2_vars = {}

        for (meas_name, signal_name), var in list(self.signal_check_vars.items()):
            if meas_name == old_name:
                old_signal_vars[signal_name] = var
                del self.signal_check_vars[(meas_name, signal_name)]
                self.signal_check_vars[(new_name, signal_name)] = var

        for (meas_name, signal_name), var in list(self.signal_y2_vars.items()):
            if meas_name == old_name:
                old_y2_vars[signal_name] = var
                del self.signal_y2_vars[(meas_name, signal_name)]
                self.signal_y2_vars[(new_name, signal_name)] = var

        # Update lines dictionary
        for line_key in list(self.lines.keys()):
            if line_key.startswith(f"{old_name}_"):
                signal_name = line_key[len(old_name)+1:]
                new_key = f"{new_name}_{signal_name}"
                self.lines[new_key] = self.lines.pop(line_key)

        # Refresh the tree and plot
        self.populate_measurement_tree()
        self.draw_plot()

        # Show success message in status (if available)
        print(f"Measurement renamed from '{old_name}' to '{new_name}'")

    def update_y2_state(self):
        """Update Y2 axis controls based on whether any signals use Y2"""
        # Use the comprehensive Y2 controls state method
        self.update_y2_controls_state()





    def toggle_all_signals(self):
        """Toggle all signals on/off"""
        # Check if any signal is currently enabled
        any_enabled = any(var.get() for var in self.signal_check_vars.values())
        new_state = not any_enabled

        # Update all signal variables and tree display
        for (measurement_name, signal_name), var in self.signal_check_vars.items():
            var.set(new_state)
            # Find the tree item and update display
            for measurement_item in self.measurement_tree.get_children():
                if self.measurement_tree.item(measurement_item)['text'] == measurement_name:
                    for signal_item in self.measurement_tree.get_children(measurement_item):
                        if self.measurement_tree.item(signal_item)['text'] == signal_name:
                            self.measurement_tree.set(signal_item, 'enabled', '✅' if new_state else '⬜️')

        self.draw_plot()

    def toggle_all_y2(self):
        """Toggle all signals Y2 on/off"""
        # Check if any signal is currently on Y2
        any_y2 = any(var.get() for var in self.signal_y2_vars.values())
        new_state = not any_y2

        # Update all Y2 variables and tree display
        for (measurement_name, signal_name), var in self.signal_y2_vars.items():
            var.set(new_state)
            # Find the tree item and update display
            for measurement_item in self.measurement_tree.get_children():
                if self.measurement_tree.item(measurement_item)['text'] == measurement_name:
                    for signal_item in self.measurement_tree.get_children(measurement_item):
                        if self.measurement_tree.item(signal_item)['text'] == signal_name:
                            self.measurement_tree.set(signal_item, 'y2', '✅' if new_state else '⬜️')

        self.update_y2_state()
        self.draw_plot()
    def draw_plot(self):
        """Main plotting function - recreated from PlottingApp"""
        self.ax.clear()
        if self.ax2:
            self.ax2.remove()
            self.ax2 = None

        self.lines.clear()

        # Check if any signal uses Y2 axis
        use_y2 = any(var.get() for var in self.signal_y2_vars.values())

        if use_y2:
            self.ax2 = self.ax.twinx()

        # Get style settings
        try:
            linewidth = float(self.linewidth_var.get())
        except ValueError:
            linewidth = 2.0

        marker = marker_map.get(self.marker_var.get(), '')
        linestyle = linestyle_map.get(self.linestyle_var.get(), '-')

        # Color palette
        palette_name = self.color_palette_var.get()
        if palette_name == "default":
            colors = plt.cm.tab10.colors
        else:
            try:
                colors = getattr(plt.cm, palette_name).colors
            except:
                colors = plt.cm.tab10.colors

        color_index = 0

        # Plot enabled signals
        for measurement in self.measurements:
            for signal_name, signal_info in measurement.y_signals.items():
                # Get the checkbox values for this signal
                enabled_var = self.signal_check_vars.get((measurement.name, signal_name))
                y2_var = self.signal_y2_vars.get((measurement.name, signal_name))

                if enabled_var and enabled_var.get():
                    x_data = measurement.x
                    y_data = signal_info["data"]

                    # Choose axis
                    use_y2_axis = y2_var.get() if y2_var else False
                    axis = self.ax2 if use_y2_axis and self.ax2 else self.ax

                    # Generate label
                    label_mode = self.label_mode_var.get()
                    if label_mode == "file":
                        label = measurement.name
                    elif label_mode == "header":
                        label = signal_name
                    else:
                        label = f"{measurement.name} - {signal_name}"

                    # Plot line
                    color = colors[color_index % len(colors)]
                    line, = axis.plot(x_data, y_data,
                                     marker=marker,
                                     linestyle=linestyle,
                                     linewidth=linewidth,
                                     color=color,
                                     label=label)

                    self.lines[f"{measurement.name}_{signal_name}"] = line
                    color_index += 1

        # Apply styling
        self.apply_plot_styling()

        # Refresh canvas
        self.canvas.draw()

    def apply_plot_styling(self):
        """Apply styling to the plot"""
        # Title with custom size
        title = self.title_entry.get()
        if title:
            try:
                title_size = int(self.title_size_var.get())
            except ValueError:
                title_size = 14
            self.fig.suptitle(title, fontsize=title_size, fontfamily=self.font_var.get())

        # Axis labels
        self.ax.set_xlabel(self.x_label_entry.get(), fontsize=int(self.fontsize_var.get()), fontfamily=self.font_var.get())
        self.ax.set_ylabel(self.y1_label_entry.get(), fontsize=int(self.fontsize_var.get()), fontfamily=self.font_var.get())

        if self.ax2:
            self.ax2.set_ylabel(self.y2_label_entry.get(), fontsize=int(self.fontsize_var.get()), fontfamily=self.font_var.get())

        # Tick sizes and advanced tick settings
        tick_size = int(self.ticksize_var.get())

        # Configure tick parameters with individual opposite ticks options
        x_opposite = self.x_opposite_ticks_var.get()
        y_opposite = self.y_opposite_ticks_var.get()
        y2_opposite = self.y2_opposite_ticks_var.get()

        # Apply tick parameters for primary axis
        self.ax.tick_params(labelsize=tick_size, top=x_opposite, right=y_opposite)

        # Apply tick parameters for secondary axis if it exists
        if self.ax2:
            self.ax2.tick_params(labelsize=tick_size, top=x_opposite, left=y2_opposite)

        # Apply custom tick spacing
        self.apply_tick_spacing()

        # Apply scientific notation
        self.apply_scientific_notation()

        # Grid
        if self.grid_var.get():
            self.ax.grid(True, alpha=0.3)

        # Axis limits
        try:
            x_min = float(self.lower_x_lim_entry.get()) if self.lower_x_lim_entry.get() else None
            x_max = float(self.upper_x_lim_entry.get()) if self.upper_x_lim_entry.get() else None
            if x_min is not None or x_max is not None:
                self.ax.set_xlim(x_min, x_max)
        except ValueError:
            pass

        try:
            y1_min = float(self.lower_y1_lim_entry.get()) if self.lower_y1_lim_entry.get() else None
            y1_max = float(self.upper_y1_lim_entry.get()) if self.upper_y1_lim_entry.get() else None
            if y1_min is not None or y1_max is not None:
                self.ax.set_ylim(y1_min, y1_max)
        except ValueError:
            pass

        if self.ax2:
            try:
                y2_min = float(self.lower_y2_lim_entry.get()) if self.lower_y2_lim_entry.get() else None
                y2_max = float(self.upper_y2_lim_entry.get()) if self.upper_y2_lim_entry.get() else None
                if y2_min is not None or y2_max is not None:
                    self.ax2.set_ylim(y2_min, y2_max)
            except ValueError:
                pass

        # Log scales
        if self.log_x_var.get():
            self.ax.set_xscale("log")
        if self.log_y_var.get():
            self.ax.set_yscale("log")
        if self.log_y2_var.get() and self.ax2:
            self.ax2.set_yscale("log")

        # Legend with custom size
        if self.lines:
            legend_pos = self.legend_pos_var.get()
            try:
                legend_size = int(self.legend_size_var.get())
            except ValueError:
                legend_size = 10
            self.ax.legend(loc=legend_pos, fontsize=legend_size)

    def apply_tick_spacing(self):
        """Apply custom tick spacing to axes"""
        import matplotlib.ticker as ticker

        # X-axis tick spacing
        x_spacing = self.x_tick_spacing_var.get().strip()
        if x_spacing and x_spacing.lower() != "auto":
            try:
                spacing = float(x_spacing)
                self.ax.xaxis.set_major_locator(ticker.MultipleLocator(spacing))
            except ValueError:
                pass  # Invalid input, keep auto spacing

        # Y-axis tick spacing
        y_spacing = self.y_tick_spacing_var.get().strip()
        if y_spacing and y_spacing.lower() != "auto":
            try:
                spacing = float(y_spacing)
                self.ax.yaxis.set_major_locator(ticker.MultipleLocator(spacing))
            except ValueError:
                pass  # Invalid input, keep auto spacing

        # Y2-axis tick spacing
        if self.ax2:
            y2_spacing = self.y2_tick_spacing_var.get().strip()
            if y2_spacing and y2_spacing.lower() != "auto":
                try:
                    spacing = float(y2_spacing)
                    self.ax2.yaxis.set_major_locator(ticker.MultipleLocator(spacing))
                except ValueError:
                    pass  # Invalid input, keep auto spacing

    def apply_scientific_notation(self):
        """Apply scientific notation formatting to axes"""
        import matplotlib.ticker as ticker

        # X-axis scientific notation
        if self.x_scientific_notation_var.get():
            self.ax.xaxis.set_major_formatter(ticker.ScalarFormatter(useMathText=True))
            self.ax.ticklabel_format(style='scientific', axis='x', scilimits=(0,0))

        # Y-axis scientific notation
        if self.y_scientific_notation_var.get():
            self.ax.yaxis.set_major_formatter(ticker.ScalarFormatter(useMathText=True))
            self.ax.ticklabel_format(style='scientific', axis='y', scilimits=(0,0))

        # Y2-axis scientific notation
        if self.ax2 and self.y2_scientific_notation_var.get():
            self.ax2.yaxis.set_major_formatter(ticker.ScalarFormatter(useMathText=True))
            self.ax2.ticklabel_format(style='scientific', axis='y', scilimits=(0,0))



    def save_config(self):
        """Save current configuration to dictionary (internal use)"""
        config = {
            "x_label": self.x_label_entry.get(),
            "y1_label": self.y1_label_entry.get(),
            "y2_label": self.y2_label_entry.get(),
            "title": self.title_entry.get(),
            "x_lim": [self.lower_x_lim_entry.get(), self.upper_x_lim_entry.get()],
            "y1_lim": [self.lower_y1_lim_entry.get(), self.upper_y1_lim_entry.get()],
            "y2_lim": [self.lower_y2_lim_entry.get(), self.upper_y2_lim_entry.get()],
            "grid": self.grid_var.get(),
            "legend_position": self.legend_pos_var.get(),
            "font": self.font_var.get(),
            "fontsize": int(self.fontsize_var.get()),
            "ticksize": int(self.ticksize_var.get()),
            "title_size": int(self.title_size_var.get()),
            "legend_size": int(self.legend_size_var.get()),
            "x_tick_spacing": self.x_tick_spacing_var.get(),
            "y_tick_spacing": self.y_tick_spacing_var.get(),
            "y2_tick_spacing": self.y2_tick_spacing_var.get(),
            "x_opposite_ticks": self.x_opposite_ticks_var.get(),
            "y_opposite_ticks": self.y_opposite_ticks_var.get(),
            "y2_opposite_ticks": self.y2_opposite_ticks_var.get(),
            "x_scientific_notation": self.x_scientific_notation_var.get(),
            "y_scientific_notation": self.y_scientific_notation_var.get(),
            "y2_scientific_notation": self.y2_scientific_notation_var.get(),
            "marker": self.marker_var.get(),
            "line_style": self.linestyle_var.get(),
            "line_width": float(self.linewidth_var.get()),
            "color_palette_name": self.color_palette_var.get(),
            "log_x": self.log_x_var.get(),
            "log_y": self.log_y_var.get(),
            "log_y2": self.log_y2_var.get()
        }
        return config

    def load_config(self, config):
        """Load configuration from dictionary (internal use)"""
        try:
            # Apply loaded configuration
            self.x_label_entry.delete(0, tk.END)
            self.x_label_entry.insert(0, config.get("x_label", ""))
            self.y1_label_entry.delete(0, tk.END)
            self.y1_label_entry.insert(0, config.get("y1_label", ""))
            self.y2_label_entry.delete(0, tk.END)
            self.y2_label_entry.insert(0, config.get("y2_label", ""))
            self.title_entry.delete(0, tk.END)
            self.title_entry.insert(0, config.get("title", ""))

            # Axis limits
            x_lim = config.get("x_lim", ["", ""])
            self.lower_x_lim_entry.delete(0, tk.END)
            self.lower_x_lim_entry.insert(0, x_lim[0])
            self.upper_x_lim_entry.delete(0, tk.END)
            self.upper_x_lim_entry.insert(0, x_lim[1])

            y1_lim = config.get("y1_lim", ["", ""])
            self.lower_y1_lim_entry.delete(0, tk.END)
            self.lower_y1_lim_entry.insert(0, y1_lim[0])
            self.upper_y1_lim_entry.delete(0, tk.END)
            self.upper_y1_lim_entry.insert(0, y1_lim[1])

            y2_lim = config.get("y2_lim", ["", ""])
            self.lower_y2_lim_entry.delete(0, tk.END)
            self.lower_y2_lim_entry.insert(0, y2_lim[0])
            self.upper_y2_lim_entry.delete(0, tk.END)
            self.upper_y2_lim_entry.insert(0, y2_lim[1])

            # Other settings
            self.grid_var.set(config.get("grid", True))
            self.legend_pos_var.set(config.get("legend_position", "best"))
            self.font_var.set(config.get("font", "Arial"))
            self.fontsize_var.set(str(config.get("fontsize", 12)))
            self.ticksize_var.set(str(config.get("ticksize", 10)))
            self.title_size_var.set(str(config.get("title_size", 14)))
            self.legend_size_var.set(str(config.get("legend_size", 10)))
            self.x_tick_spacing_var.set(config.get("x_tick_spacing", "auto"))
            self.y_tick_spacing_var.set(config.get("y_tick_spacing", "auto"))
            self.y2_tick_spacing_var.set(config.get("y2_tick_spacing", "auto"))
            self.x_opposite_ticks_var.set(config.get("x_opposite_ticks", False))
            self.y_opposite_ticks_var.set(config.get("y_opposite_ticks", False))
            self.y2_opposite_ticks_var.set(config.get("y2_opposite_ticks", False))
            self.x_scientific_notation_var.set(config.get("x_scientific_notation", False))
            self.y_scientific_notation_var.set(config.get("y_scientific_notation", False))
            self.y2_scientific_notation_var.set(config.get("y2_scientific_notation", False))
            self.marker_var.set(config.get("marker", "None"))
            self.linestyle_var.set(config.get("line_style", "Solid"))
            self.linewidth_var.set(str(config.get("line_width", 2.0)))
            self.color_palette_var.set(config.get("color_palette_name", "default"))
            self.log_x_var.set(config.get("log_x", False))
            self.log_y_var.set(config.get("log_y", False))
            self.log_y2_var.set(config.get("log_y2", False))

            # Update Y2 controls state and redraw plot
            self.update_y2_state()
            self.draw_plot()

        except Exception as e:
            print(f"Error loading configuration: {str(e)}")

    def export_project(self):
        """Save project to structured directory system"""
        try:
            # Check if project directory is still accessible
            if not check_directory_permissions(self.project_base_dir):
                messagebox.showerror("Directory Access Error",
                                   f"Cannot access project directory:\n{self.project_base_dir}\n\n"
                                   "Please restart the application to select a new project directory in setting.")
                return

            # Ensure project base directory exists
            os.makedirs(self.project_base_dir, exist_ok=True)

            # Step 1: Select or create project (contains multiple measurements)
            project_name = self.select_or_create_project()
            if not project_name:
                return

            # Step 2: Select or create measurement (contains sample data)
            measurement_name = self.select_or_create_measurement(project_name)
            if not measurement_name:
                return

            # Step 3: Save all sample data to measurement
            self.save_project_data(project_name, measurement_name)

            # Ask user if they want to close the plotting app after saving
            result = messagebox.askyesno("Save Complete",
                                       f"Sample data saved to:\nProject: {project_name}\nMeasurement: {measurement_name}\n\n"
                                       "Structure: Projects → Measurements → Sample Data\n\n"
                                       "Would you like to close the plotting application?")

            if result:  # User clicked Yes
                self.master.destroy()

        except Exception as e:
            messagebox.showerror("Error", f"Failed to save project: {str(e)}")


    def select_or_create_project(self):
        """Let user select existing project or create new one"""
        # Get existing projects
        existing_projects = []
        if os.path.exists(self.project_base_dir):
            existing_projects = [d for d in os.listdir(self.project_base_dir)
                               if os.path.isdir(os.path.join(self.project_base_dir, d))]

        # Create selection dialog
        dialog = tk.Toplevel(self.master)
        dialog.title("Select Project")
        dialog.geometry("400x300")
        dialog.transient(self.master)
        dialog.grab_set()

        # Center the dialog
        dialog.geometry("+%d+%d" % (self.master.winfo_rootx() + 50, self.master.winfo_rooty() + 50))

        result = {"project": None}

        # Main frame
        main_frame = ttk.Frame(dialog)
        main_frame.pack(fill="both", expand=True, padx=10, pady=10)

        # Instructions
        ttk.Label(main_frame, text="To which project?", font=("Arial", 12, "bold")).pack(pady=(0, 10))

        # Project list
        list_frame = ttk.Frame(main_frame)
        list_frame.pack(fill="both", expand=True, pady=(0, 10))

        # Listbox with scrollbar
        scrollbar = ttk.Scrollbar(list_frame)
        scrollbar.pack(side="right", fill="y")

        project_listbox = tk.Listbox(list_frame, yscrollcommand=scrollbar.set)
        project_listbox.pack(side="left", fill="both", expand=True)
        scrollbar.config(command=project_listbox.yview)

        # Populate listbox
        for project in existing_projects:
            project_listbox.insert(tk.END, project)

        # Buttons frame
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill="x", pady=(10, 0))

        def on_select():
            selection = project_listbox.curselection()
            if selection:
                result["project"] = project_listbox.get(selection[0])
                dialog.destroy()

        def on_create_new():
            new_name = tk.simpledialog.askstring("New Project", "Enter project name:", parent=dialog)
            if new_name and new_name.strip():
                # Validate project name
                new_name = new_name.strip()
                if any(char in new_name for char in ['/', '\\', ':', '*', '?', '"', '<', '>', '|']):
                    messagebox.showerror("Invalid Name", "Project name contains invalid characters")
                    return
                result["project"] = new_name
                dialog.destroy()

        def on_cancel():
            dialog.destroy()

        ttk.Button(button_frame, text="Select", command=on_select).pack(side="left", padx=(0, 5))
        ttk.Button(button_frame, text="Create New", command=on_create_new).pack(side="left", padx=5)
        ttk.Button(button_frame, text="Cancel", command=on_cancel).pack(side="right")

        # Bind double-click to select
        project_listbox.bind('<Double-Button-1>', lambda e: on_select())

        # Wait for dialog to close
        dialog.wait_window()

        return result["project"]

    def select_or_create_measurement(self, project_name):
        """Let user select existing measurement or create new one"""
        project_path = os.path.join(self.project_base_dir, project_name)

        # Get existing measurements
        existing_measurements = []
        if os.path.exists(project_path):
            existing_measurements = [d for d in os.listdir(project_path)
                                   if os.path.isdir(os.path.join(project_path, d))]

        # Create selection dialog
        dialog = tk.Toplevel(self.master)
        dialog.title("Select Measurement")
        dialog.geometry("400x300")
        dialog.transient(self.master)
        dialog.grab_set()

        # Center the dialog
        dialog.geometry("+%d+%d" % (self.master.winfo_rootx() + 50, self.master.winfo_rooty() + 50))

        result = {"measurement": None}

        # Main frame
        main_frame = ttk.Frame(dialog)
        main_frame.pack(fill="both", expand=True, padx=10, pady=10)

        # Instructions
        ttk.Label(main_frame, text=f"To which measurement in '{project_name}'?",
                 font=("Arial", 12, "bold")).pack(pady=(0, 10))

        # Measurement list
        list_frame = ttk.Frame(main_frame)
        list_frame.pack(fill="both", expand=True, pady=(0, 10))

        # Listbox with scrollbar
        scrollbar = ttk.Scrollbar(list_frame)
        scrollbar.pack(side="right", fill="y")

        measurement_listbox = tk.Listbox(list_frame, yscrollcommand=scrollbar.set)
        measurement_listbox.pack(side="left", fill="both", expand=True)
        scrollbar.config(command=measurement_listbox.yview)

        # Populate listbox
        for measurement in existing_measurements:
            measurement_listbox.insert(tk.END, measurement)

        # Buttons frame
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill="x", pady=(10, 0))

        def on_select():
            selection = measurement_listbox.curselection()
            if selection:
                result["measurement"] = measurement_listbox.get(selection[0])
                dialog.destroy()

        def on_create_new():
            new_name = tk.simpledialog.askstring("New Measurement", "Enter measurement name:", parent=dialog)
            if new_name and new_name.strip():
                # Validate measurement name
                new_name = new_name.strip()
                if any(char in new_name for char in ['/', '\\', ':', '*', '?', '"', '<', '>', '|']):
                    messagebox.showerror("Invalid Name", "Measurement name contains invalid characters")
                    return
                result["measurement"] = new_name
                dialog.destroy()

        def on_cancel():
            dialog.destroy()

        ttk.Button(button_frame, text="Select", command=on_select).pack(side="left", padx=(0, 5))
        ttk.Button(button_frame, text="Create New", command=on_create_new).pack(side="left", padx=5)
        ttk.Button(button_frame, text="Cancel", command=on_cancel).pack(side="right")

        # Bind double-click to select
        measurement_listbox.bind('<Double-Button-1>', lambda e: on_select())

        # Wait for dialog to close
        dialog.wait_window()

        return result["measurement"]

    def save_project_data(self, project_name, measurement_name):
        """Save all project data to the structured directory"""
        # Create directory structure
        project_path = os.path.join(self.project_base_dir, project_name)
        measurement_path = os.path.join(project_path, measurement_name)
        data_path = os.path.join(measurement_path, "Data")

        os.makedirs(data_path, exist_ok=True)

        # Create backup directory for temporary files
        backup_path = os.path.join(project_path, "backup")
        os.makedirs(backup_path, exist_ok=True)

        # Backup files list for cleanup
        backup_files = []

        try:
            # 1. Save plot preview to backup first
            preview_filename = f"{os.path.basename(measurement_path)}_preview.png"
            backup_preview_path = os.path.join(backup_path, preview_filename)
            final_preview_path = os.path.join(measurement_path, preview_filename)

            self.fig.savefig(backup_preview_path, dpi=300, bbox_inches='tight')
            backup_files.append((backup_preview_path, final_preview_path))
            print(f"Preview saved to backup: {backup_preview_path}")

            # 2. Save plot configuration to backup first
            config_filename = "plot_config.json"
            backup_config_path = os.path.join(backup_path, config_filename)
            final_config_path = os.path.join(measurement_path, config_filename)

            config = self.save_config()
            with open(backup_config_path, "w") as f:
                json.dump(config, f, indent=4)
            backup_files.append((backup_config_path, final_config_path))

            # 3. Save metadata to backup first
            metadata = {
                "project_name": project_name,
                "measurement_name": measurement_name,
                "created_date": datetime.datetime.now().isoformat(),
                "plot_type": "2D",
                "data_files": [],
                "measurement_count": len(self.measurements),
                "signal_count": sum(len(m.y_signals) for m in self.measurements)
            }

            # 4. Save measurement data to backup first
            for measurement in self.measurements:
                # Use current measurement name (which can be renamed)
                filename = f"{measurement.name}.csv"
                backup_data_path = os.path.join(backup_path, filename)
                final_data_path = os.path.join(data_path, filename)

                try:
                    # Convert to DataFrame and save
                    df_result = measurement.to_dataframe()
                    df_result.to_csv(backup_data_path, index=False)
                    backup_files.append((backup_data_path, final_data_path))
                    metadata["data_files"].append(filename)
                except Exception as e:
                    # Provide detailed error information
                    error_msg = f"Error saving measurement '{measurement.name}': {str(e)}"
                    error_msg += f"\nMeasurement type: {type(measurement)}"
                    error_msg += f"\nMeasurement is Measurement object: {isinstance(measurement, Measurement)}"
                    if hasattr(measurement, 'to_dataframe'):
                        try:
                            df_result = measurement.to_dataframe()
                            error_msg += f"\nto_dataframe() returned type: {type(df_result)}"
                            error_msg += f"\nto_dataframe() returned DataFrame: {isinstance(df_result, pd.DataFrame)}"
                        except Exception as df_error:
                            error_msg += f"\nto_dataframe() failed: {str(df_error)}"
                    else:
                        error_msg += f"\nMeasurement has no to_dataframe method"

                    # Show detailed error and re-raise
                    messagebox.showerror("Save Error", error_msg)
                    raise

            # Save metadata to backup
            metadata_filename = "metadata.json"
            backup_metadata_path = os.path.join(backup_path, metadata_filename)
            final_metadata_path = os.path.join(measurement_path, metadata_filename)

            with open(backup_metadata_path, "w") as f:
                json.dump(metadata, f, indent=4)
            backup_files.append((backup_metadata_path, final_metadata_path))

            # Move all files from backup to final location
            for backup_file, final_file in backup_files:
                import shutil
                shutil.move(backup_file, final_file)
                print(f"Moved {backup_file} to {final_file}")

            # Clean up backup directory if empty
            try:
                os.rmdir(backup_path)
                print(f"Backup directory cleaned up: {backup_path}")
            except OSError:
                # Directory not empty, leave it
                pass

        except Exception as e:
            # Clean up backup files on error
            for backup_file, _ in backup_files:
                try:
                    if os.path.exists(backup_file):
                        os.remove(backup_file)
                except:
                    pass
            raise e

        # 5. Save processing log (placeholder for now)
        processing_log = {
            "created_date": datetime.datetime.now().isoformat(),
            "operations": [
                {
                    "timestamp": datetime.datetime.now().isoformat(),
                    "operation": "project_save",
                    "details": f"Saved to project '{project_name}', measurement '{measurement_name}'"
                }
            ]
        }

        log_path = os.path.join(measurement_path, "processing_log.json")
        with open(log_path, "w") as f:
            json.dump(processing_log, f, indent=4)

    # Helper methods for processing
    def get_selected_signals(self):
        """Get list of selected signals from treeview"""
        selected_signals = []
        for measurement in self.measurements:
            for signal_name in measurement.y_signals:
                item_id = f"{measurement.name}_{signal_name}"
                if item_id in self.tree_items and self.tree.set(self.tree_items[item_id], "Select") == "✓":
                    selected_signals.append((measurement.name, signal_name))
        return selected_signals

    def get_normalize_method(self):
        """Get normalization method from user"""
        dialog = tk.Toplevel(self.master)
        dialog.title("Normalize Data")
        dialog.geometry("300x200")
        dialog.transient(self.master)
        dialog.grab_set()

        method_var = tk.StringVar(value="min_max")
        result = [None]

        ttk.Label(dialog, text="Normalization Method:").pack(pady=10)
        methods = [("Min-Max", "min_max"), ("Z-Score", "z_score"), ("Custom", "custom")]
        for text, value in methods:
            ttk.Radiobutton(dialog, text=text, variable=method_var, value=value).pack(anchor='w', padx=20)

        def ok_clicked():
            result[0] = method_var.get()
            dialog.destroy()

        def cancel_clicked():
            dialog.destroy()

        button_frame = ttk.Frame(dialog)
        button_frame.pack(pady=20)
        ttk.Button(button_frame, text="OK", command=ok_clicked).pack(side='left', padx=5)
        ttk.Button(button_frame, text="Cancel", command=cancel_clicked).pack(side='left', padx=5)

        dialog.wait_window()
        return result[0]

    # Editing methods (implemented using ProcessingMethods)
    def normalize_data(self):
        """Normalize data values"""
        try:
            from ProcessingMethods import DataProcessor

            # Get selected signals
            selected_signals = self.get_selected_signals()
            if not selected_signals:
                messagebox.showwarning("Warning", "Please select signals to normalize")
                return

            # Get normalization method from user
            method = self.get_normalize_method()
            if not method:
                return

            # Process each selected signal
            for measurement_name, signal_name in selected_signals:
                measurement = next(m for m in self.measurements if m.name == measurement_name)

                # Create temporary dataframe
                temp_data = pd.DataFrame({
                    'X': measurement.x,
                    signal_name: measurement.y_signals[signal_name]
                })

                # Normalize
                normalized_data = DataProcessor.normalize_data(temp_data, method=method, column=signal_name)

                # Update measurement
                measurement.y_signals[signal_name]["data"] = normalized_data[signal_name].values

            # Refresh plot
            self.update_plot()
            messagebox.showinfo("Success", f"Normalized {len(selected_signals)} signal(s)")

        except Exception as e:
            messagebox.showerror("Error", f"Normalization failed: {str(e)}")

    def get_smooth_parameters(self):
        """Get smoothing parameters from user"""
        dialog = tk.Toplevel(self.master)
        dialog.title("Smooth Data")
        dialog.geometry("300x250")
        dialog.transient(self.master)
        dialog.grab_set()

        method_var = tk.StringVar(value="moving_average")
        window_var = tk.StringVar(value="5")
        result = [None]

        ttk.Label(dialog, text="Smoothing Method:").pack(pady=5)
        methods = [("Moving Average", "moving_average"), ("Gaussian", "gaussian")]
        for text, value in methods:
            ttk.Radiobutton(dialog, text=text, variable=method_var, value=value).pack(anchor='w', padx=20)

        ttk.Label(dialog, text="Window Size:").pack(pady=(10, 0))
        ttk.Entry(dialog, textvariable=window_var).pack(pady=5)

        def ok_clicked():
            try:
                result[0] = {
                    'method': method_var.get(),
                    'window_size': int(window_var.get())
                }
                dialog.destroy()
            except ValueError:
                messagebox.showerror("Error", "Invalid window size")

        def cancel_clicked():
            dialog.destroy()

        button_frame = ttk.Frame(dialog)
        button_frame.pack(pady=10)
        ttk.Button(button_frame, text="OK", command=ok_clicked).pack(side='left', padx=5)
        ttk.Button(button_frame, text="Cancel", command=cancel_clicked).pack(side='left', padx=5)

        dialog.wait_window()
        return result[0]

    def smooth_data(self):
        """Apply smoothing to data"""
        try:
            from ProcessingMethods import DataProcessor

            # Get selected signals
            selected_signals = self.get_selected_signals()
            if not selected_signals:
                messagebox.showwarning("Warning", "Please select signals to smooth")
                return

            # Get smoothing parameters from user
            params = self.get_smooth_parameters()
            if not params:
                return

            # Process each selected signal
            for measurement_name, signal_name in selected_signals:
                measurement = next(m for m in self.measurements if m.name == measurement_name)

                # Create temporary dataframe
                temp_data = pd.DataFrame({
                    'X': measurement.x,
                    signal_name: measurement.y_signals[signal_name]
                })

                # Smooth
                smoothed_data = DataProcessor.smooth_data(temp_data,
                                                        method=params['method'],
                                                        window_size=params['window_size'],
                                                        column=signal_name)

                # Update measurement
                measurement.y_signals[signal_name]["data"] = smoothed_data[signal_name].values

            # Refresh plot
            self.update_plot()
            messagebox.showinfo("Success", f"Smoothed {len(selected_signals)} signal(s)")

        except Exception as e:
            messagebox.showerror("Error", f"Smoothing failed: {str(e)}")

    def get_crop_parameters(self):
        """Get crop parameters from user"""
        dialog = tk.Toplevel(self.master)
        dialog.title("Crop Data")
        dialog.geometry("350x300")
        dialog.transient(self.master)
        dialog.grab_set()

        # Variables
        x_min_var = tk.StringVar()
        x_max_var = tk.StringVar()
        y_min_var = tk.StringVar()
        y_max_var = tk.StringVar()
        result = [None]

        # X range
        ttk.Label(dialog, text="X Range:").pack(pady=(10, 5))
        x_frame = ttk.Frame(dialog)
        x_frame.pack(pady=5)
        ttk.Label(x_frame, text="Min:").pack(side='left')
        ttk.Entry(x_frame, textvariable=x_min_var, width=10).pack(side='left', padx=5)
        ttk.Label(x_frame, text="Max:").pack(side='left', padx=(10, 0))
        ttk.Entry(x_frame, textvariable=x_max_var, width=10).pack(side='left', padx=5)

        # Y range
        ttk.Label(dialog, text="Y Range:").pack(pady=(10, 5))
        y_frame = ttk.Frame(dialog)
        y_frame.pack(pady=5)
        ttk.Label(y_frame, text="Min:").pack(side='left')
        ttk.Entry(y_frame, textvariable=y_min_var, width=10).pack(side='left', padx=5)
        ttk.Label(y_frame, text="Max:").pack(side='left', padx=(10, 0))
        ttk.Entry(y_frame, textvariable=y_max_var, width=10).pack(side='left', padx=5)

        ttk.Label(dialog, text="(Leave empty for no limit)", font=('Arial', 8)).pack(pady=5)

        def ok_clicked():
            try:
                params = {}
                if x_min_var.get():
                    params['x_min'] = float(x_min_var.get())
                if x_max_var.get():
                    params['x_max'] = float(x_max_var.get())
                if y_min_var.get():
                    params['y_min'] = float(y_min_var.get())
                if y_max_var.get():
                    params['y_max'] = float(y_max_var.get())

                result[0] = params
                dialog.destroy()
            except ValueError:
                messagebox.showerror("Error", "Invalid parameter values")

        def cancel_clicked():
            dialog.destroy()

        button_frame = ttk.Frame(dialog)
        button_frame.pack(pady=20)
        ttk.Button(button_frame, text="OK", command=ok_clicked).pack(side='left', padx=5)
        ttk.Button(button_frame, text="Cancel", command=cancel_clicked).pack(side='left', padx=5)

        dialog.wait_window()
        return result[0]

    def crop_data(self):
        """Crop data to selected region"""
        try:
            from ProcessingMethods import DataProcessor

            # Get selected signals
            selected_signals = self.get_selected_signals()
            if not selected_signals:
                messagebox.showwarning("Warning", "Please select signals to crop")
                return

            # Get crop parameters from user
            params = self.get_crop_parameters()
            if not params:
                return

            # Process each selected signal
            for measurement_name, signal_name in selected_signals:
                measurement = next(m for m in self.measurements if m.name == measurement_name)

                # Create temporary dataframe
                temp_data = pd.DataFrame({
                    'X': measurement.x,
                    signal_name: measurement.y_signals[signal_name]
                })

                # Crop
                cropped_data = DataProcessor.crop_data(temp_data,
                                                     x_column='X',
                                                     x_min=params.get('x_min'),
                                                     x_max=params.get('x_max'),
                                                     y_column=signal_name,
                                                     y_min=params.get('y_min'),
                                                     y_max=params.get('y_max'))

                # Update measurement
                measurement.x = cropped_data['X'].values
                measurement.y_signals[signal_name]["data"] = cropped_data[signal_name].values

            # Refresh plot
            self.update_plot()
            messagebox.showinfo("Success", f"Cropped {len(selected_signals)} signal(s)")

        except Exception as e:
            messagebox.showerror("Error", f"Cropping failed: {str(e)}")

    def shift_data(self):
        """Shift data values"""
        messagebox.showinfo("Shift", "Data shifting functionality - to be implemented")

    def apply_fft(self):
        """Apply FFT transformation"""
        try:
            from ProcessingMethods import DataProcessor

            # Get selected signals
            selected_signals = self.get_selected_signals()
            if not selected_signals:
                messagebox.showwarning("Warning", "Please select signals for FFT")
                return

            # Process each selected signal
            for measurement_name, signal_name in selected_signals:
                measurement = next(m for m in self.measurements if m.name == measurement_name)

                # Create temporary dataframe
                temp_data = pd.DataFrame({
                    'X': measurement.x,
                    signal_name: measurement.y_signals[signal_name]
                })

                # Apply FFT
                fft_result = DataProcessor.fourier_transform(temp_data, 'X', signal_name, transform_type='fft')

                # Create new measurement for FFT result
                fft_name = f"{measurement_name}_FFT"
                fft_measurement = Measurement(fft_name,
                                            fft_result['Frequency'].values,
                                            {f"{signal_name}_FFT": fft_result['Magnitude'].values})

                # Add to measurements
                self.measurements.append(fft_measurement)

                # Add to tree
                self.add_measurement_to_tree(fft_measurement)

            # Refresh plot
            self.update_plot()
            messagebox.showinfo("Success", f"Applied FFT to {len(selected_signals)} signal(s)")

        except Exception as e:
            messagebox.showerror("Error", f"FFT failed: {str(e)}")

    def filter_data(self):
        """Apply data filtering"""
        messagebox.showinfo("Filter", "Data filtering functionality - to be implemented")

    def baseline_correction(self):
        """Apply baseline correction"""
        try:
            from ProcessingMethods import DataProcessor

            # Get selected signals
            selected_signals = self.get_selected_signals()
            if not selected_signals:
                messagebox.showwarning("Warning", "Please select signals for baseline correction")
                return

            # Get baseline method from user
            method = self.get_baseline_method()
            if not method:
                return

            # Process each selected signal
            for measurement_name, signal_name in selected_signals:
                measurement = next(m for m in self.measurements if m.name == measurement_name)

                # Create temporary dataframe
                temp_data = pd.DataFrame({
                    'X': measurement.x,
                    signal_name: measurement.y_signals[signal_name]
                })

                # Create baseline
                baseline_data = DataProcessor.create_baseline(temp_data, 'X', signal_name,
                                                            method=method['method'],
                                                            degree=method.get('degree', 1))

                # Subtract baseline
                corrected_data = DataProcessor.subtract_baseline(baseline_data, signal_name, 'Baseline')

                # Update measurement
                measurement.y_signals[signal_name]["data"] = corrected_data[f"{signal_name}_Corrected"].values

            # Refresh plot
            self.update_plot()
            messagebox.showinfo("Success", f"Applied baseline correction to {len(selected_signals)} signal(s)")

        except Exception as e:
            messagebox.showerror("Error", f"Baseline correction failed: {str(e)}")

    def get_baseline_method(self):
        """Get baseline correction method from user"""
        dialog = tk.Toplevel(self.master)
        dialog.title("Baseline Correction")
        dialog.geometry("300x200")
        dialog.transient(self.master)
        dialog.grab_set()

        method_var = tk.StringVar(value="linear")
        degree_var = tk.StringVar(value="1")
        result = [None]

        ttk.Label(dialog, text="Baseline Method:").pack(pady=10)
        methods = [("Linear", "linear"), ("Polynomial", "polynomial")]
        for text, value in methods:
            ttk.Radiobutton(dialog, text=text, variable=method_var, value=value).pack(anchor='w', padx=20)

        ttk.Label(dialog, text="Polynomial Degree:").pack(pady=(10, 0))
        ttk.Entry(dialog, textvariable=degree_var).pack(pady=5)

        def ok_clicked():
            try:
                result[0] = {
                    'method': method_var.get(),
                    'degree': int(degree_var.get())
                }
                dialog.destroy()
            except ValueError:
                messagebox.showerror("Error", "Invalid degree value")

        def cancel_clicked():
            dialog.destroy()

        button_frame = ttk.Frame(dialog)
        button_frame.pack(pady=20)
        ttk.Button(button_frame, text="OK", command=ok_clicked).pack(side='left', padx=5)
        ttk.Button(button_frame, text="Cancel", command=cancel_clicked).pack(side='left', padx=5)

        dialog.wait_window()
        return result[0]

    def deconvolute_data(self):
        """Deconvolute data"""
        messagebox.showinfo("Deconvolute", "Data deconvolution functionality - to be implemented")

    def show_statistics(self):
        """Show data statistics"""
        messagebox.showinfo("Statistics", "Data statistics functionality - to be implemented")

    def find_peaks(self):
        """Find peaks in data"""
        messagebox.showinfo("Peak Find", "Peak finding functionality - to be implemented")

    def integrate_data(self):
        """Integrate data"""
        messagebox.showinfo("Integrate", "Data integration functionality - to be implemented")

    def fit_curve(self):
        """Fit curve to data"""
        messagebox.showinfo("Fit Curve", "Curve fitting functionality - to be implemented")

    def subtract_data(self):
        """Subtract data from other data"""
        messagebox.showinfo("Subtract", "Data subtraction functionality - to be implemented")

    def add_data(self):
        """Add data to other data"""
        messagebox.showinfo("Add", "Data addition functionality - to be implemented")

    def multiply_data(self):
        """Multiply data"""
        messagebox.showinfo("Multiply", "Data multiplication functionality - to be implemented")

    def custom_formula(self):
        """Apply custom formula to data"""
        messagebox.showinfo("Formula", "Custom formula functionality - to be implemented")

    def export_data(self):
        """Export processed data"""
        messagebox.showinfo("Export Data", "Data export functionality - to be implemented")

    def export_plot(self):
        """Export plot as image"""
        messagebox.showinfo("Export Plot", "Plot export functionality - to be implemented")

# Test function
def test_2d_plotting():
    """Test function for 2D plotting window"""
    root = tk.Tk()
    root.withdraw()  # Hide main window

    # Create sample data
    x = np.linspace(0, 10, 100)
    data = pd.DataFrame({
        'x': x,
        'sin(x)': np.sin(x),
        'cos(x)': np.cos(x),
        'tan(x)': np.tan(x)
    })

    config = {
        "title": "Test 2D Plot",
        "x_label": "X Values",
        "y1_label": "Y Values"
    }

    plot_window = tk.Toplevel()
    app = Plot2DWindow(plot_window, data, config)

    root.mainloop()

if __name__ == "__main__":
    test_2d_plotting()
