#!/usr/bin/env python3
"""
Test script to verify 3D array dimension alignment fix
"""

import pandas as pd
import numpy as np

def test_dimension_alignment():
    """Test the dimension alignment logic for 3D array format"""
    print("=== Testing 3D Array Dimension Alignment ===\n")

    # Create test data that simulates the CORRECT 3D array format
    # Based on PlottingApp3D test: first row = X, first col = Y, rest = Z

    print("Creating test 3D array data (correct format)...")

    # X coordinates: [1, 2, 3] (3 points)
    # Y coordinates: [1, 2, 3, 4] (4 points)
    # Expected Z matrix: 4x3 (4 rows for Y, 3 columns for X)

    # Create data matrix like in PlottingApp3D test
    x_coords = np.array([1, 2, 3])
    y_coords = np.array([1, 2, 3, 4])

    # Create Z matrix: 4x3 (rows=Y, cols=X)
    z_values = np.array([
        [10, 20, 30],  # Z values for Y=1
        [11, 21, 31],  # Z values for Y=2
        [12, 22, 32],  # Z values for Y=3
        [13, 23, 33]   # Z values for Y=4
    ])

    # Build the complete data matrix (like ImportWizard format)
    data_matrix = np.full((len(y_coords) + 1, len(x_coords) + 1), np.nan)
    data_matrix[0, 1:] = x_coords  # X coordinates in first row (skip first cell)
    data_matrix[1:, 0] = y_coords  # Y coordinates in first column (skip first cell)
    data_matrix[1:, 1:] = z_values  # Z data in remaining matrix

    test_data = pd.DataFrame(data_matrix)

    print("Test data matrix:")
    print(test_data)
    print(f"Data shape: {test_data.shape}")
    print()

    # Simulate the extraction logic (X from row, Y from column)
    print("--- Simulating Array Extraction Logic ---")

    # X from first row (excluding first cell) - this is x_type='row'
    x_data = test_data.iloc[0, 1:]  # Skip first column
    x_clean = x_data.dropna().values
    print(f"X coordinates: {x_clean} (length: {len(x_clean)})")

    # Y from first column (excluding first cell) - this is y_type='column'
    y_data = test_data.iloc[1:, 0]  # Skip first row
    y_clean = y_data.dropna().values
    print(f"Y coordinates: {y_clean} (length: {len(y_clean)})")

    # Z matrix (excluding first row and first column)
    z_data = test_data.iloc[1:, 1:]
    z_matrix = z_data.values
    print(f"Z matrix shape: {z_matrix.shape}")
    print("Z matrix:")
    print(z_matrix)
    print()
    
    # Check dimension alignment
    print("--- Checking Dimension Alignment ---")
    expected_shape = (len(y_clean), len(x_clean))
    print(f"Expected Z shape: {expected_shape} (rows=Y_length, cols=X_length)")
    print(f"Actual Z shape: {z_matrix.shape}")

    if z_matrix.shape == expected_shape:
        print("✓ Dimensions are correctly aligned!")
        z_final = z_matrix
    else:
        print("✗ Dimension mismatch detected!")

        # Apply the NEW fix logic from ImportWizard
        print("\nApplying NEW dimension fix logic...")

        # For x_type='row' and y_type='column' (most common case)
        # Z matrix should already have correct dimensions after extraction
        if z_matrix.shape == expected_shape:
            z_final = z_matrix
            print("✓ No fix needed")
        elif z_matrix.shape == (expected_shape[1], expected_shape[0]):
            z_final = z_matrix.T
            print("✓ Applied transpose")
        elif z_matrix.size == expected_shape[0] * expected_shape[1]:
            z_final = z_matrix.reshape(expected_shape)
            print("✓ Applied reshape")
        elif z_matrix.shape[0] >= expected_shape[0] and z_matrix.shape[1] >= expected_shape[1]:
            z_final = z_matrix[:expected_shape[0], :expected_shape[1]]
            print("✓ Applied crop")
        else:
            z_final = z_matrix
            print("✗ Cannot fix dimension mismatch")

        print(f"Final Z shape: {z_final.shape}")
        if z_final.shape == expected_shape:
            print("✓ Fix successful!")
        else:
            print("✗ Fix failed!")

    print()
    
    # Test coordinate grid creation
    print("--- Testing Coordinate Grid ---")
    X, Y = np.meshgrid(x_clean, y_clean)
    print(f"Meshgrid X shape: {X.shape}")
    print(f"Meshgrid Y shape: {Y.shape}")
    print(f"Final Z shape: {z_final.shape}")

    if X.shape == Y.shape == z_final.shape:
        print("✓ All arrays have matching shapes for 3D plotting!")
    else:
        print("✗ Shape mismatch for 3D plotting!")
        print(f"  Meshgrid: {X.shape}")
        print(f"  Z matrix: {z_final.shape}")

    print("\nMeshgrid X:")
    print(X)
    print("\nMeshgrid Y:")
    print(Y)
    print("\nFinal Z values:")
    print(z_final)

    # Test a specific coordinate mapping
    print("\n--- Testing Coordinate Mapping ---")
    print("Checking if Z[i,j] corresponds to (X[j], Y[i]):")
    for i in range(min(2, len(y_clean))):  # Test first 2 Y coordinates
        for j in range(min(2, len(x_clean))):  # Test first 2 X coordinates
            x_val = x_clean[j]
            y_val = y_clean[i]
            z_val = z_final[i, j]
            print(f"  Point ({x_val}, {y_val}) -> Z = {z_val}")

    return z_final

def test_alternative_format():
    """Test alternative 3D data format"""
    print("\n" + "="*50)
    print("=== Testing Alternative 3D Format ===\n")
    
    # Alternative format: X and Y as separate columns/rows
    test_data2 = pd.DataFrame({
        'Col1': [1, 2, 3],      # X coordinates
        'Col2': [1, 2, 3],      # Y coordinates  
        'Z1': [10, 20, 30],     # Z values column 1
        'Z2': [11, 21, 31],     # Z values column 2
        'Z3': [12, 22, 32]      # Z values column 3
    })
    
    print("Alternative test data:")
    print(test_data2)
    print()
    
    # Extract X, Y, Z
    x_data = test_data2['Col1'].values
    y_data = test_data2['Col2'].values
    z_cols = ['Z1', 'Z2', 'Z3']
    z_data = test_data2[z_cols]
    z_matrix = z_data.values
    
    print(f"X: {x_data} (length: {len(x_data)})")
    print(f"Y: {y_data} (length: {len(y_data)})")
    print(f"Z matrix shape: {z_matrix.shape}")
    print("Z matrix:")
    print(z_matrix)
    
    # Check if this makes sense for 3D plotting
    print(f"\nFor 3D plotting:")
    print(f"X length: {len(x_data)}")
    print(f"Y length: {len(y_data)}")
    print(f"Z should be: ({len(y_data)}, {len(x_data)}) = {(len(y_data), len(x_data))}")
    print(f"Z actual: {z_matrix.shape}")
    
    if z_matrix.shape == (len(y_data), len(x_data)):
        print("✓ Dimensions match!")
    else:
        print("✗ Dimensions don't match - need transpose or reshape")

if __name__ == "__main__":
    test_dimension_alignment()
    test_alternative_format()
