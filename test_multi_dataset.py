#!/usr/bin/env python3
"""
Test script for multi-dataset functionality in PlottingApp system.
This script creates sample data and tests the multi-dataset workflow.
"""

import pandas as pd
import numpy as np
import os
import json
from pathlib import Path

def create_test_data():
    """Create test data files for multi-dataset testing"""
    
    # Create test directory
    test_dir = Path("test_data")
    test_dir.mkdir(exist_ok=True)
    
    # Test 1: 2D data with multiple Y columns (should display all in one plot)
    print("Creating 2D multi-dataset test file...")
    x_data = np.linspace(0, 10, 100)
    y1_data = np.sin(x_data)
    y2_data = np.cos(x_data)
    y3_data = np.sin(x_data * 2) * 0.5
    
    df_2d = pd.DataFrame({
        'X': x_data,
        'Sin(X)': y1_data,
        'Cos(X)': y2_data,
        'Sin(2X)/2': y3_data
    })
    
    df_2d.to_csv(test_dir / "multi_2d_data.csv", index=False)
    print(f"Created: {test_dir / 'multi_2d_data.csv'}")
    
    # Test 2: 3D data with multiple Z columns (should create separate datasets)
    print("Creating 3D multi-dataset test file...")
    x_vals = np.linspace(-2, 2, 20)
    y_vals = np.linspace(-2, 2, 20)
    X, Y = np.meshgrid(x_vals, y_vals)
    
    # Create multiple Z surfaces
    Z1 = np.sin(np.sqrt(X**2 + Y**2))  # Ripple pattern
    Z2 = np.exp(-(X**2 + Y**2))        # Gaussian
    Z3 = X**2 + Y**2                   # Paraboloid
    
    # Flatten for CSV format
    x_flat = X.flatten()
    y_flat = Y.flatten()
    z1_flat = Z1.flatten()
    z2_flat = Z2.flatten()
    z3_flat = Z3.flatten()
    
    df_3d = pd.DataFrame({
        'X': x_flat,
        'Y': y_flat,
        'Ripple_Z': z1_flat,
        'Gaussian_Z': z2_flat,
        'Paraboloid_Z': z3_flat
    })
    
    df_3d.to_csv(test_dir / "multi_3d_data.csv", index=False)
    print(f"Created: {test_dir / 'multi_3d_data.csv'}")
    
    # Test 3: Mixed data (some 2D, some 3D columns)
    print("Creating mixed dataset test file...")
    df_mixed = pd.DataFrame({
        'Time': np.linspace(0, 5, 50),
        'Signal1': np.sin(np.linspace(0, 5, 50) * 2),
        'Signal2': np.cos(np.linspace(0, 5, 50) * 3),
        'X_coord': np.random.uniform(-1, 1, 50),
        'Y_coord': np.random.uniform(-1, 1, 50),
        'Z_value': np.random.normal(0, 1, 50)
    })
    
    df_mixed.to_csv(test_dir / "mixed_data.csv", index=False)
    print(f"Created: {test_dir / 'mixed_data.csv'}")
    
    return test_dir

def test_config_creation():
    """Test the config creation for multi-dataset scenarios"""
    print("\nTesting config creation...")
    
    # Simulate ImportWizard config creation for 3D multi-dataset
    datasets = [
        pd.DataFrame({'X': [1, 2], 'Y': [3, 4], 'Z': [5, 6]}),
        pd.DataFrame({'X': [7, 8], 'Y': [9, 10], 'Z': [11, 12]}),
        pd.DataFrame({'X': [13, 14], 'Y': [15, 16], 'Z': [17, 18]})
    ]
    
    # Set dataset names
    datasets[0].name = "Ripple"
    datasets[1].name = "Gaussian"
    datasets[2].name = "Paraboloid"
    
    # Create configs like ImportWizard would
    configs = []
    for i, dataset in enumerate(datasets):
        plot_config = {
            "plot_type": "contour",
            "colormap": "viridis",
            "alpha": 1.0,
            "colorbar": True
        }
        
        config = {
            "title": f"Test Analysis - Dataset {i+1}",
            "plot_config": plot_config,
            "dimension": 3,
            "dataset_index": i,
            "total_datasets": len(datasets),
            "import_settings": {
                "delimiter": ",",
                "decimal_sep": ".",
                "header_line": 0,
                "data_start": 1
            }
        }
        configs.append(config)
    
    master_config = {
        "title": "Test Analysis",
        "dimension": 3,
        "datasets": datasets,
        "configs": configs,
        "multi_dataset": True
    }
    
    print(f"Created master config with {len(datasets)} datasets")
    print(f"Multi-dataset flag: {master_config['multi_dataset']}")
    print(f"Individual configs: {len(master_config['configs'])}")
    
    return master_config

def test_2d_multi_dataset():
    """Test 2D multi-dataset configuration"""
    print("\nTesting 2D multi-dataset config...")
    
    # Create multiple 2D datasets
    datasets = []
    for i in range(3):
        x = np.linspace(0, 10, 50)
        y = np.sin(x + i) * (i + 1)
        df = pd.DataFrame({'X': x, f'Y_dataset_{i+1}': y})
        df.name = f"Dataset_{i+1}"
        datasets.append(df)
    
    config = {
        "title": "2D Multi-Dataset Test",
        "dimension": 2,
        "datasets": datasets,
        "multi_dataset": True
    }
    
    print(f"Created 2D config with {len(datasets)} datasets")
    print(f"All datasets should display in one plot")
    
    return config

def main():
    """Main test function"""
    print("=== Multi-Dataset Functionality Test ===\n")
    
    # Create test data files
    test_dir = create_test_data()
    
    # Test config creation
    config_3d = test_config_creation()
    config_2d = test_2d_multi_dataset()
    
    print("\n=== Test Summary ===")
    print("✓ Created test data files for 2D and 3D multi-dataset scenarios")
    print("✓ Tested config creation for 3D multi-dataset (individual configs)")
    print("✓ Tested config creation for 2D multi-dataset (single plot)")
    print("\nTest files created in:", test_dir.absolute())
    print("\nTo test manually:")
    print("1. Run DataAnalysisApp.py")
    print("2. Import the test files using ImportWizard")
    print("3. Verify multi-dataset handling:")
    print("   - 2D data: All datasets in one plot")
    print("   - 3D data: Navigation controls with dropdown/buttons")
    print("   - Individual configs for 3D datasets")
    print("   - Apply-to-all functionality")
    print("   - Contourf mode option")

if __name__ == "__main__":
    main()
