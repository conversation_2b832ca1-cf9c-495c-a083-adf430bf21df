#!/usr/bin/env python3
"""
Debug script to find the exact source of the 'list object has no to_csv' error
"""

import pandas as pd
import numpy as np
import sys
import os
import traceback

# Add current directory to path
sys.path.insert(0, os.getcwd())

def debug_measurement_creation():
    """Debug the Measurement class creation and to_dataframe method"""
    print("=== Debugging Measurement Class ===")
    
    try:
        from PlottingApp2D import Measurement
        
        # Test normal measurement creation
        x_data = np.linspace(0, 10, 50)
        y_data = np.sin(x_data)
        y_dict = {"sin_wave": y_data}
        
        measurement = Measurement("test_measurement", x_data, y_dict)
        print(f"✓ Measurement created: {measurement.name}")
        print(f"  X type: {type(measurement.x)}")
        print(f"  Y signals type: {type(measurement.y_signals)}")
        
        # Test to_dataframe
        df = measurement.to_dataframe()
        print(f"✓ to_dataframe() successful: {df.shape}")
        print(f"  DataFrame columns: {list(df.columns)}")
        
        # Test to_csv
        import tempfile
        temp_file = tempfile.mktemp(suffix='.csv')
        df.to_csv(temp_file, index=False)
        print(f"✓ to_csv() successful")
        os.unlink(temp_file)
        
        return True
        
    except Exception as e:
        print(f"✗ Measurement test failed: {str(e)}")
        traceback.print_exc()
        return False

def debug_multi_dataset_creation():
    """Debug multi-dataset creation to see if lists are being passed incorrectly"""
    print("\n=== Debugging Multi-Dataset Creation ===")
    
    try:
        from PlottingApp2D import Plot2DWindow, Measurement
        import tkinter as tk
        
        # Create test datasets as list
        datasets = []
        for i, func_name in enumerate(['sin', 'cos']):
            x_data = np.linspace(0, 10, 50)
            if func_name == 'sin':
                y_data = np.sin(x_data)
            else:
                y_data = np.cos(x_data)
                
            df = pd.DataFrame({
                'X': x_data,
                'Y': y_data
            })
            df.name = f"{func_name}_wave"
            datasets.append(df)
        
        print(f"✓ Created {len(datasets)} datasets")
        for i, ds in enumerate(datasets):
            print(f"  Dataset {i}: {type(ds)} - {ds.shape}")
        
        # Create config
        config = {
            "title": "Debug Multi-Dataset",
            "dimension": 2,
            "multi_dataset": True,
            "total_datasets": len(datasets),
            "dataset_names": [df.name for df in datasets]
        }
        
        # Create PlottingApp2D
        root = tk.Tk()
        root.withdraw()
        plot_window = tk.Toplevel()
        plot_window.withdraw()
        
        app = Plot2DWindow(plot_window, datasets, config)
        print(f"✓ PlottingApp2D created")
        print(f"  Multi-dataset: {app.multi_dataset}")
        print(f"  Datasets count: {len(app.datasets)}")
        print(f"  Measurements count: {len(app.measurements)}")
        
        # Check each measurement
        for i, measurement in enumerate(app.measurements):
            print(f"  Measurement {i}: {measurement.name}")
            print(f"    Type: {type(measurement)}")
            print(f"    X type: {type(measurement.x)}")
            print(f"    Y signals type: {type(measurement.y_signals)}")
            
            # Check if any y_signals contain lists instead of arrays
            for signal_name, signal_info in measurement.y_signals.items():
                data_type = type(signal_info["data"])
                print(f"    Signal '{signal_name}' data type: {data_type}")
                if isinstance(signal_info["data"], list):
                    print(f"    ⚠ WARNING: Signal data is a list, not array!")
                    return False
            
            # Test to_dataframe on each measurement
            try:
                df = measurement.to_dataframe()
                print(f"    ✓ to_dataframe() successful: {df.shape}")
            except Exception as e:
                print(f"    ✗ to_dataframe() failed: {str(e)}")
                traceback.print_exc()
                return False
        
        plot_window.destroy()
        root.destroy()
        
        return True
        
    except Exception as e:
        print(f"✗ Multi-dataset test failed: {str(e)}")
        traceback.print_exc()
        return False

def debug_save_operation():
    """Debug the save operation to see where to_csv might be called on a list"""
    print("\n=== Debugging Save Operation ===")
    
    try:
        from PlottingApp2D import Plot2DWindow
        import tkinter as tk
        
        # Create simple test data
        x_data = np.linspace(0, 10, 20)
        y_data = np.sin(x_data)
        
        df = pd.DataFrame({
            'X': x_data,
            'Y': y_data
        })
        
        config = {
            "title": "Debug Save",
            "dimension": 2,
            "multi_dataset": False
        }
        
        # Create PlottingApp2D
        root = tk.Tk()
        root.withdraw()
        plot_window = tk.Toplevel()
        plot_window.withdraw()
        
        app = Plot2DWindow(plot_window, df, config)
        print(f"✓ PlottingApp2D created for save test")
        print(f"  Measurements: {len(app.measurements)}")
        
        # Check the measurement data types
        for measurement in app.measurements:
            print(f"  Measurement: {measurement.name}")
            print(f"    X type: {type(measurement.x)}")
            print(f"    Y signals: {list(measurement.y_signals.keys())}")
            
            for signal_name, signal_info in measurement.y_signals.items():
                print(f"    Signal '{signal_name}': {type(signal_info['data'])}")
                
                # Check if it's a list (this would cause the error)
                if isinstance(signal_info['data'], list):
                    print(f"    ✗ FOUND THE PROBLEM: Signal data is a list!")
                    print(f"      List contents: {signal_info['data'][:5]}...")
                    return False
            
            # Test the to_dataframe method
            try:
                df_result = measurement.to_dataframe()
                print(f"    ✓ to_dataframe() works: {df_result.shape}")
                
                # Test to_csv
                import tempfile
                temp_file = tempfile.mktemp(suffix='.csv')
                df_result.to_csv(temp_file, index=False)
                print(f"    ✓ to_csv() works")
                os.unlink(temp_file)
                
            except Exception as e:
                print(f"    ✗ Error in to_dataframe/to_csv: {str(e)}")
                traceback.print_exc()
                return False
        
        plot_window.destroy()
        root.destroy()
        
        return True
        
    except Exception as e:
        print(f"✗ Save operation test failed: {str(e)}")
        traceback.print_exc()
        return False

def main():
    """Run all debug tests"""
    print("Debugging 'list object has no to_csv' Error")
    print("=" * 50)
    
    test1 = debug_measurement_creation()
    test2 = debug_multi_dataset_creation()
    test3 = debug_save_operation()
    
    print("\n" + "=" * 50)
    if test1 and test2 and test3:
        print("✓ All debug tests passed - no obvious source of to_csv error found")
        print("\nPossible causes:")
        print("1. Error occurs in a specific workflow not tested here")
        print("2. Error occurs in 3D plotting (not tested here)")
        print("3. Error occurs in a specific save/export operation")
        print("\nPlease provide the exact steps that trigger the error.")
    else:
        print("✗ Found potential issues - check output above")
    
    return 0

if __name__ == "__main__":
    exit(main())
