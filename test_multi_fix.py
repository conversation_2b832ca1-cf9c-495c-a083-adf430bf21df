#!/usr/bin/env python3
"""
Test script to verify multi-dataset fixes work correctly.
Tests the data passing mechanism without JSON serialization issues.
"""

import pandas as pd
import numpy as np
import sys
import os

# Add current directory to path
sys.path.insert(0, os.getcwd())

def test_2d_multi_dataset():
    """Test 2D multi-dataset functionality"""
    print("=== Testing 2D Multi-Dataset ===")
    
    try:
        # Create test datasets
        x_data = np.linspace(0, 10, 50)
        
        # Dataset 1
        df1 = pd.DataFrame({
            'X': x_data,
            'Y': np.sin(x_data)
        })
        df1.name = "Sin_Wave"
        
        # Dataset 2  
        df2 = pd.DataFrame({
            'X': x_data,
            'Y': np.cos(x_data)
        })
        df2.name = "Cos_Wave"
        
        # Dataset 3
        df3 = pd.DataFrame({
            'X': x_data,
            'Y': np.sin(x_data * 2) * 0.5
        })
        df3.name = "Sin_2X"
        
        datasets = [df1, df2, df3]
        
        # Create config (JSON-serializable)
        config = {
            "title": "2D Multi-Dataset Test",
            "dimension": 2,
            "multi_dataset": True,
            "total_datasets": len(datasets),
            "dataset_names": ["Sin_Wave", "Cos_Wave", "Sin_2X"],
            "plot_config": {
                "plot_type": "line",
                "colormap": "viridis"
            }
        }
        
        print(f"✓ Created {len(datasets)} 2D datasets")
        print(f"✓ Config is JSON-serializable: {config['multi_dataset']}")
        print(f"✓ Dataset names: {config['dataset_names']}")
        
        # Test PlottingApp2D constructor
        import tkinter as tk
        from PlottingApp2D import Plot2DWindow
        
        root = tk.Tk()
        root.withdraw()  # Hide the window
        
        # This should work without JSON serialization error
        plot_window = tk.Toplevel()
        plot_window.withdraw()  # Hide the window
        
        app = Plot2DWindow(plot_window, datasets, config)
        print(f"✓ PlottingApp2D created successfully")
        print(f"✓ Multi-dataset flag: {app.multi_dataset}")
        print(f"✓ Number of datasets: {len(app.datasets)}")
        
        plot_window.destroy()
        root.destroy()
        
        return True
        
    except Exception as e:
        print(f"✗ 2D Multi-Dataset test failed: {str(e)}")
        return False

def test_3d_multi_dataset():
    """Test 3D multi-dataset functionality"""
    print("\n=== Testing 3D Multi-Dataset ===")
    
    try:
        # Create test 3D datasets
        x_vals = np.linspace(-2, 2, 10)
        y_vals = np.linspace(-2, 2, 10)
        X, Y = np.meshgrid(x_vals, y_vals)
        
        # Dataset 1 - Ripple
        Z1 = np.sin(np.sqrt(X**2 + Y**2))
        df1 = pd.DataFrame({
            'X': X.flatten(),
            'Y': Y.flatten(),
            'Z': Z1.flatten()
        })
        df1.name = "Ripple"
        
        # Dataset 2 - Gaussian
        Z2 = np.exp(-(X**2 + Y**2))
        df2 = pd.DataFrame({
            'X': X.flatten(),
            'Y': Y.flatten(),
            'Z': Z2.flatten()
        })
        df2.name = "Gaussian"
        
        datasets = [df1, df2]
        
        # Create individual configs for each dataset
        configs = []
        for i, dataset in enumerate(datasets):
            config = {
                "title": f"3D Test - {dataset.name}",
                "plot_config": {
                    "plot_type": "surface",
                    "colormap": "viridis"
                },
                "dimension": 3,
                "dataset_index": i,
                "total_datasets": len(datasets),
                "dataset_name": dataset.name
            }
            configs.append(config)
        
        # Master config (JSON-serializable)
        master_config = {
            "title": "3D Multi-Dataset Test",
            "dimension": 3,
            "multi_dataset": True,
            "total_datasets": len(datasets),
            "dataset_names": ["Ripple", "Gaussian"],
            "configs": configs
        }
        
        print(f"✓ Created {len(datasets)} 3D datasets")
        print(f"✓ Config is JSON-serializable: {master_config['multi_dataset']}")
        print(f"✓ Individual configs: {len(master_config['configs'])}")
        
        # Test PlottingApp3D constructor
        import tkinter as tk
        from PlottingApp3D import Plot3DWindow
        
        root = tk.Tk()
        root.withdraw()  # Hide the window
        
        # This should work without JSON serialization error
        plot_window = tk.Toplevel()
        plot_window.withdraw()  # Hide the window
        
        app = Plot3DWindow(plot_window, datasets, master_config)
        print(f"✓ PlottingApp3D created successfully")
        print(f"✓ Multi-dataset flag: {app.multi_dataset}")
        print(f"✓ Number of datasets: {len(app.datasets)}")
        print(f"✓ Number of configs: {len(app.dataset_configs)}")
        
        plot_window.destroy()
        root.destroy()
        
        return True
        
    except Exception as e:
        print(f"✗ 3D Multi-Dataset test failed: {str(e)}")
        return False

def main():
    """Run all tests"""
    print("Testing Multi-Dataset Fixes")
    print("=" * 40)
    
    success_2d = test_2d_multi_dataset()
    success_3d = test_3d_multi_dataset()
    
    print("\n" + "=" * 40)
    if success_2d and success_3d:
        print("🎉 All tests passed! Multi-dataset functionality is working.")
        print("\nKey fixes implemented:")
        print("✓ Removed DataFrames from JSON config")
        print("✓ Pass datasets as data parameter")
        print("✓ JSON-serializable configs only")
        print("✓ Proper multi-dataset detection")
    else:
        print("❌ Some tests failed. Check the errors above.")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
