#!/usr/bin/env python3
"""
Test script to verify the fix for 'list has no to_csv function' error.
This script tests the data flow from ImportWizard through DataAnalysisApp to plotting apps.
"""

import os
import sys
import tempfile
import pandas as pd
import numpy as np
import traceback
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def create_test_datasets():
    """Create test datasets that simulate ImportWizard output"""
    print("Creating test datasets...")
    
    # Create multiple datasets (simulating 3D multi-file import)
    datasets = []
    
    for i in range(3):
        x_data = np.linspace(0, 10, 50)
        y_data = np.sin(x_data + i * np.pi/3)  # Phase shift for each dataset
        
        df = pd.DataFrame({
            'X': x_data,
            'Y': y_data
        })
        df.name = f"Dataset_{i+1}"
        datasets.append(df)
    
    print(f"✓ Created {len(datasets)} test datasets")
    return datasets

def test_data_analysis_app_callback():
    """Test the DataAnalysisApp.on_import_complete callback with list of DataFrames"""
    print("\n=== Testing DataAnalysisApp Callback ===")
    
    try:
        from DataAnalysisApp import DataAnalysisApp
        import tkinter as tk
        
        # Create test data (list of DataFrames)
        datasets = create_test_datasets()
        
        # Create config that indicates multi-dataset
        config = {
            "title": "Test Multi-Dataset",
            "dimension": 2,
            "multi_dataset": True,
            "total_datasets": len(datasets),
            "dataset_names": [df.name for df in datasets]
        }
        
        # Create a temporary root window
        root = tk.Tk()
        root.withdraw()  # Hide it
        
        # Create DataAnalysisApp instance
        app = DataAnalysisApp(root)
        
        # Test the create_temporary_project method directly
        print("Testing create_temporary_project with list of DataFrames...")
        
        # This should NOT raise the 'list has no to_csv function' error anymore
        app.create_temporary_project(datasets, config, "test_file.csv")
        
        print("✓ create_temporary_project succeeded with list of DataFrames")
        
        # Verify that the temporary project was created
        if app.current_project:
            temp_path = app.current_project["temp_path"]
            temp_data_file = os.path.join(temp_path, "temp_data.csv")
            
            if os.path.exists(temp_data_file):
                print("✓ Temporary data file was created successfully")
                
                # Verify the file contains valid CSV data
                test_df = pd.read_csv(temp_data_file)
                print(f"✓ Temporary CSV file is valid: {test_df.shape}")
            else:
                print("✗ Temporary data file was not created")
                return False
        else:
            print("✗ Current project was not set")
            return False
        
        # Clean up
        root.destroy()
        
        return True
        
    except Exception as e:
        print(f"✗ DataAnalysisApp callback test failed: {str(e)}")
        traceback.print_exc()
        return False

def test_plotting_integration():
    """Test the PlottingIntegration with list of DataFrames"""
    print("\n=== Testing PlottingIntegration ===")
    
    try:
        from PlottingIntegration import PlottingLauncher
        import tkinter as tk
        
        # Create test data (list of DataFrames)
        datasets = create_test_datasets()
        
        # Create config
        config = {
            "title": "Test Multi-Dataset",
            "dimension": 2,
            "multi_dataset": True,
            "total_datasets": len(datasets)
        }
        
        # Create a temporary root window
        root = tk.Tk()
        root.withdraw()  # Hide it
        
        print("Testing PlottingLauncher with list of DataFrames...")
        
        # This should handle the list properly
        plotting_app = PlottingLauncher.launch_plotting_window(
            data=datasets,
            dimension=2,
            title="Test Multi-Dataset",
            config=config
        )
        
        if plotting_app:
            print("✓ PlottingLauncher succeeded with list of DataFrames")
            print(f"✓ Created plotting app with {len(plotting_app.measurements)} measurements")
            
            # Test each measurement's to_dataframe method
            for i, measurement in enumerate(plotting_app.measurements):
                df = measurement.to_dataframe()
                print(f"✓ Measurement {i} to_dataframe() works: {df.shape}")
            
            # Clean up
            plotting_app.master.destroy()
        else:
            print("✗ PlottingLauncher returned None")
            return False
        
        # Clean up
        root.destroy()
        
        return True
        
    except Exception as e:
        print(f"✗ PlottingIntegration test failed: {str(e)}")
        traceback.print_exc()
        return False

def test_single_dataframe_compatibility():
    """Test that single DataFrame still works (backward compatibility)"""
    print("\n=== Testing Single DataFrame Compatibility ===")
    
    try:
        from DataAnalysisApp import DataAnalysisApp
        import tkinter as tk
        
        # Create single DataFrame (not a list)
        x_data = np.linspace(0, 10, 50)
        y_data = np.sin(x_data)
        single_df = pd.DataFrame({'X': x_data, 'Y': y_data})
        
        config = {
            "title": "Test Single Dataset",
            "dimension": 2,
            "multi_dataset": False
        }
        
        # Create a temporary root window
        root = tk.Tk()
        root.withdraw()  # Hide it
        
        # Create DataAnalysisApp instance
        app = DataAnalysisApp(root)
        
        print("Testing create_temporary_project with single DataFrame...")
        
        # This should still work as before
        app.create_temporary_project(single_df, config, "test_single.csv")
        
        print("✓ create_temporary_project succeeded with single DataFrame")
        
        # Clean up
        root.destroy()
        
        return True
        
    except Exception as e:
        print(f"✗ Single DataFrame compatibility test failed: {str(e)}")
        traceback.print_exc()
        return False

def main():
    """Run all tests to verify the fix"""
    print("Testing 'list has no to_csv function' Fix")
    print("=" * 50)
    
    test1 = test_data_analysis_app_callback()
    test2 = test_plotting_integration()
    test3 = test_single_dataframe_compatibility()
    
    print("\n" + "=" * 50)
    if test1 and test2 and test3:
        print("🎉 All tests passed! The 'list has no to_csv function' error is fixed.")
        print("\nKey fixes verified:")
        print("✓ DataAnalysisApp.create_temporary_project handles lists of DataFrames")
        print("✓ PlottingIntegration works with lists of DataFrames")
        print("✓ Backward compatibility with single DataFrames maintained")
        print("✓ Measurement classes properly convert lists to arrays")
    else:
        print("❌ Some tests failed. Check the errors above.")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
