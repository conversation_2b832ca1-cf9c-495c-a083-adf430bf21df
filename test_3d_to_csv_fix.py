#!/usr/bin/env python3
"""
Test script to verify the 3D to_csv error is fixed
"""

import pandas as pd
import numpy as np
import sys
import os
import traceback

# Add current directory to path
sys.path.insert(0, os.getcwd())

def test_measurement3d_with_lists():
    """Test Measurement3D creation with list data to ensure it's converted to arrays"""
    print("=== Testing Measurement3D with List Data ===")
    
    try:
        from PlottingApp3D import Measurement3D
        
        # Create test data as lists (this would cause the error before)
        x_data = [1, 2, 3, 4, 5]
        y_data = [1, 2, 3, 4, 5]
        z_data_list = [[1, 2, 3, 4, 5],
                       [2, 3, 4, 5, 6],
                       [3, 4, 5, 6, 7],
                       [4, 5, 6, 7, 8],
                       [5, 6, 7, 8, 9]]
        
        z_dict = {"test_signal": z_data_list}
        
        print(f"✓ Created test data as lists")
        print(f"  X type: {type(x_data)}")
        print(f"  Y type: {type(y_data)}")
        print(f"  Z type: {type(z_data_list)}")
        
        # Create Measurement3D - this should convert lists to arrays
        measurement = Measurement3D("test_measurement", x_data, y_data, z_dict)
        
        print(f"✓ Measurement3D created successfully")
        print(f"  X type after: {type(measurement.x)}")
        print(f"  Y type after: {type(measurement.y)}")
        
        # Check Z signal data type
        for signal_name, signal_info in measurement.z_signals.items():
            print(f"  Z signal '{signal_name}' type: {type(signal_info['data'])}")
            if isinstance(signal_info['data'], list):
                print(f"  ✗ ERROR: Z signal is still a list!")
                return False
            else:
                print(f"  ✓ Z signal properly converted to: {type(signal_info['data'])}")
        
        return True
        
    except Exception as e:
        print(f"✗ Measurement3D list test failed: {str(e)}")
        traceback.print_exc()
        return False

def test_3d_save_operation():
    """Test 3D save operation that previously caused to_csv error"""
    print("\n=== Testing 3D Save Operation ===")
    
    try:
        from PlottingApp3D import Measurement3D
        import tempfile
        
        # Create test data that might cause issues
        x_data = np.array([1, 2, 3])
        y_data = np.array([1, 2, 3])
        
        # Test with different Z data types
        test_cases = [
            ("numpy_array", np.array([[1, 2, 3], [4, 5, 6], [7, 8, 9]])),
            ("list_2d", [[1, 2, 3], [4, 5, 6], [7, 8, 9]]),
            ("list_1d", [1, 2, 3, 4, 5, 6, 7, 8, 9])
        ]
        
        for case_name, z_data in test_cases:
            print(f"\n  Testing case: {case_name}")
            print(f"    Z data type: {type(z_data)}")
            
            z_dict = {"test_signal": z_data}
            measurement = Measurement3D(f"test_{case_name}", x_data, y_data, z_dict)
            
            # Test the problematic code path - creating DataFrame from signal data
            for signal_name, signal_info in measurement.z_signals.items():
                signal_data = signal_info["data"]
                print(f"    Signal data type after init: {type(signal_data)}")
                
                # This is the code that was failing before
                try:
                    # Test the fixed DataFrame creation logic
                    if isinstance(signal_data, list):
                        signal_data = np.array(signal_data)
                    
                    if isinstance(signal_data, np.ndarray) and signal_data.ndim == 2:
                        z_df = pd.DataFrame(signal_data)
                    elif isinstance(signal_data, np.ndarray) and signal_data.ndim == 1:
                        z_df = pd.DataFrame({signal_name: signal_data})
                    else:
                        z_df = pd.DataFrame(signal_data)
                    
                    print(f"    ✓ DataFrame created: {z_df.shape}")
                    
                    # Test to_csv
                    temp_file = tempfile.mktemp(suffix='.csv')
                    z_df.to_csv(temp_file, index=False)
                    print(f"    ✓ to_csv() successful")
                    os.unlink(temp_file)
                    
                except Exception as e:
                    print(f"    ✗ DataFrame/to_csv failed: {str(e)}")
                    return False
        
        return True
        
    except Exception as e:
        print(f"✗ 3D save test failed: {str(e)}")
        traceback.print_exc()
        return False

def test_3d_plotting_app():
    """Test 3D PlottingApp creation with multi-dataset"""
    print("\n=== Testing 3D PlottingApp Creation ===")
    
    try:
        # Create test 3D data
        x_data = np.linspace(0, 5, 10)
        y_data = np.linspace(0, 5, 10)
        
        datasets = []
        for i, func_name in enumerate(['ripple', 'gaussian']):
            # Create Z data
            X, Y = np.meshgrid(x_data, y_data)
            if func_name == 'ripple':
                Z = np.sin(np.sqrt(X**2 + Y**2))
            else:
                Z = np.exp(-(X**2 + Y**2))
            
            # Create DataFrame
            rows = []
            for j in range(len(y_data)):
                for k in range(len(x_data)):
                    rows.append({
                        'X': X[j, k],
                        'Y': Y[j, k], 
                        'Z': Z[j, k]
                    })
            
            df = pd.DataFrame(rows)
            df.name = f"{func_name}_surface"
            datasets.append(df)
        
        print(f"✓ Created {len(datasets)} 3D datasets")
        
        # Create configs
        configs = []
        for i, dataset in enumerate(datasets):
            config = {
                "title": f"3D Test - {dataset.name}",
                "dimension": 3,
                "dataset_index": i,
                "total_datasets": len(datasets),
                "dataset_name": dataset.name
            }
            configs.append(config)
        
        master_config = {
            "title": "3D Multi-Dataset Test",
            "dimension": 3,
            "multi_dataset": True,
            "total_datasets": len(datasets),
            "dataset_names": [df.name for df in datasets],
            "configs": configs
        }
        
        print(f"✓ Created multi-dataset config")
        print(f"  Multi-dataset: {master_config['multi_dataset']}")
        print(f"  Total datasets: {master_config['total_datasets']}")
        
        # Test just the data loading part (not full GUI)
        from PlottingApp3D import Plot3DWindow
        import tkinter as tk
        
        root = tk.Tk()
        root.withdraw()
        plot_window = tk.Toplevel()
        plot_window.withdraw()
        
        # This should not cause the to_csv error anymore
        app = Plot3DWindow(plot_window, datasets, master_config)
        print(f"✓ Plot3DWindow created successfully")
        print(f"  Multi-dataset: {app.multi_dataset}")
        print(f"  Current dataset index: {app.current_dataset_index}")
        
        plot_window.destroy()
        root.destroy()
        
        return True
        
    except Exception as e:
        print(f"✗ 3D PlottingApp test failed: {str(e)}")
        traceback.print_exc()
        return False

def main():
    """Run all tests to verify the to_csv fix"""
    print("Testing 3D to_csv Error Fix")
    print("=" * 40)
    
    test1 = test_measurement3d_with_lists()
    test2 = test_3d_save_operation()
    test3 = test_3d_plotting_app()
    
    print("\n" + "=" * 40)
    if test1 and test2 and test3:
        print("🎉 All tests passed! The 'list object has no to_csv' error should be fixed.")
        print("\nKey fixes implemented:")
        print("✓ Measurement3D constructor converts lists to numpy arrays")
        print("✓ add_z_signal method converts lists to numpy arrays")
        print("✓ Save operation handles different data types properly")
        print("✓ DataFrame creation is more robust")
    else:
        print("❌ Some tests failed. Check the errors above.")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
