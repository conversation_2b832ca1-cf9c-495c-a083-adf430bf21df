#!/usr/bin/env python3
"""
Main Data Analysis Application
Provides a comprehensive framework for data import, visualization, processing, and project management.
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import os
import json
import datetime
from pathlib import Path
import shutil
try:
    from PIL import Image, ImageTk
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False

# Project directory management functions
def check_directory_permissions(directory):
    """Check if we have read/write permissions for the directory"""
    try:
        # Create directory if it doesn't exist
        os.makedirs(directory, exist_ok=True)

        # Test write permission by creating a temporary file
        test_file = os.path.join(directory, '.permission_test')
        with open(test_file, 'w') as f:
            f.write('test')

        # Test read permission
        with open(test_file, 'r') as f:
            f.read()

        # Clean up test file
        os.remove(test_file)
        return True

    except (OSError, PermissionError, IOError):
        return False

def get_default_project_directory():
    """Get the default project directory"""
    return str(Path.home() / "Documents" / "PlottingApp_Projects")

class DataAnalysisApp:
    def __init__(self, root):
        self.root = root
        self.root.title("Data Analysis Application")
        self.root.geometry("800x600")
        self.root.minsize(600, 400)
        
        # Application configuration
        self.config_file = "app_config.json"
        self.config = self.load_config()

        # Project directory management
        self.project_directory_accessible = False
        self.setup_project_directory()

        # Current session management (no temporary files)
        self.current_project = None
        self.current_plotting_window = None

        # Initialize UI
        self.setup_ui()
        
    def load_config(self):
        """Load application configuration"""
        default_config = {
            "recent_projects": [],
            "default_import_directory": str(Path.home()),
            "default_export_directory": str(Path.home() / "DataAnalysisProjects"),
            "project_directory": get_default_project_directory(),
            "temp_directory": str(Path.home() / "temp" / "DataAnalysisTemp"),
            "max_recent_projects": 10,
            "auto_save_interval": 300,  # seconds
            "default_plot_settings": {
                "dpi": 300,
                "format": "png",
                "font_family": "Arial",
                "font_size": 12
            }
        }
        
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r') as f:
                    loaded_config = json.load(f)
                    # Merge with defaults to ensure all keys exist
                    default_config.update(loaded_config)
            return default_config
        except Exception as e:
            messagebox.showwarning("Config Warning", f"Could not load config: {e}\nUsing defaults.")
            return default_config
    
    def save_config(self):
        """Save application configuration"""
        try:
            with open(self.config_file, 'w') as f:
                json.dump(self.config, f, indent=4)
        except Exception as e:
            messagebox.showerror("Config Error", f"Could not save config: {e}")

    def setup_project_directory(self):
        """Setup and validate project directory on app launch"""
        project_dir = self.config.get("project_directory", get_default_project_directory())

        # Check if project directory is accessible
        if check_directory_permissions(project_dir):
            self.project_directory_accessible = True
            self.initial_status_message = f"Project directory: {project_dir}"
        else:
            self.project_directory_accessible = False
            # Don't show error immediately - will be shown in UI
            self.initial_status_message = "Project directory not accessible - check Settings"

    def setup_ui(self):
        """Setup the main user interface"""
        # Create main frame
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.pack(fill="both", expand=True)
        
        # Title
        title_label = ttk.Label(main_frame, text="Data Analysis Application", 
                               font=("Arial", 24, "bold"))
        title_label.pack(pady=(0, 30))
        
        # Subtitle
        subtitle_label = ttk.Label(main_frame, 
                                  text="Import, Visualize, Process, and Analyze Your Data",
                                  font=("Arial", 12))
        subtitle_label.pack(pady=(0, 40))
        
        # Main action buttons
        self.setup_main_buttons(main_frame)
        
        # Recent projects section
        self.setup_recent_projects(main_frame)
        
        # Status bar
        self.setup_status_bar()
        
        # Menu bar
        self.setup_menu_bar()

        # Show directory access notification if needed
        self.show_directory_notification()
    
    def setup_main_buttons(self, parent):
        """Setup main action buttons"""
        buttons_frame = ttk.Frame(parent)
        buttons_frame.pack(pady=20)
        
        # New Data button
        new_data_btn = ttk.Button(buttons_frame, text="📊 New Data Analysis", 
                                 command=self.new_data_analysis,
                                 style="Large.TButton")
        new_data_btn.pack(side="left", padx=20, ipadx=20, ipady=10)
        
        # Open Project button
        open_project_btn = ttk.Button(buttons_frame, text="📁 Open Project", 
                                     command=self.open_project,
                                     style="Large.TButton")
        open_project_btn.pack(side="left", padx=20, ipadx=20, ipady=10)
        
        # Configure button style
        style = ttk.Style()
        style.configure("Large.TButton", font=("Arial", 14))
    
    def setup_recent_projects(self, parent):
        """Setup recent projects section"""
        if not self.config.get("recent_projects"):
            return
            
        recent_frame = ttk.LabelFrame(parent, text="Recent Projects", padding="10")
        recent_frame.pack(fill="x", pady=20)

        # Add instruction label
        instruction_label = ttk.Label(recent_frame,
                                    text="💡 Tip: Double-click project name to rename, right-click for options",
                                    font=("Arial", 9),
                                    foreground="gray")
        instruction_label.pack(anchor="w", pady=(0, 5))
        
        # Create treeview for recent projects
        columns = ("Name", "Date", "Type", "Path")
        self.recent_tree = ttk.Treeview(recent_frame, columns=columns, show="headings", height=6)
        
        # Configure columns
        self.recent_tree.heading("Name", text="Project Name")
        self.recent_tree.heading("Date", text="Last Modified")
        self.recent_tree.heading("Type", text="Data Type")
        self.recent_tree.heading("Path", text="Location")
        
        self.recent_tree.column("Name", width=200)
        self.recent_tree.column("Date", width=150)
        self.recent_tree.column("Type", width=100)
        self.recent_tree.column("Path", width=300)
        
        # Scrollbar
        scrollbar = ttk.Scrollbar(recent_frame, orient="vertical", command=self.recent_tree.yview)
        self.recent_tree.configure(yscrollcommand=scrollbar.set)
        
        # Pack treeview and scrollbar
        self.recent_tree.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # Bind events
        self.recent_tree.bind("<Double-1>", self.handle_project_double_click)
        self.recent_tree.bind("<Button-3>", self.show_project_context_menu)  # Right-click

        # Create context menu
        self.project_context_menu = tk.Menu(self.root, tearoff=0)
        self.project_context_menu.add_command(label="Open Project", command=self.open_selected_project)
        self.project_context_menu.add_command(label="Rename Project", command=self.rename_selected_project)
        self.project_context_menu.add_separator()
        self.project_context_menu.add_command(label="Remove from List", command=self.remove_selected_project)

        # Populate recent projects
        self.populate_recent_projects()
    
    def setup_status_bar(self):
        """Setup status bar"""
        self.status_bar = ttk.Frame(self.root)
        self.status_bar.pack(side="bottom", fill="x")

        # Set initial status message if available
        initial_text = getattr(self, 'initial_status_message', "Ready")
        self.status_label = ttk.Label(self.status_bar, text=initial_text, relief="sunken")
        self.status_label.pack(side="left", fill="x", expand=True, padx=2, pady=2)

        # Directory status indicator
        self.directory_status_label = ttk.Label(self.status_bar, text="", relief="sunken")
        self.directory_status_label.pack(side="right", padx=2, pady=2)

        # Update directory status
        self.update_directory_status()

        # Progress bar (hidden by default)
        self.progress_bar = ttk.Progressbar(self.status_bar, mode="indeterminate")
        
    def setup_menu_bar(self):
        """Setup menu bar"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        # File menu
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="File", menu=file_menu)
        file_menu.add_command(label="New Data Analysis", command=self.new_data_analysis)
        file_menu.add_command(label="Open Project", command=self.open_project)
        file_menu.add_separator()
        file_menu.add_command(label="Settings", command=self.open_settings)
        file_menu.add_separator()
        file_menu.add_command(label="Exit", command=self.on_closing)
        
        # Tools menu
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Tools", menu=tools_menu)
        tools_menu.add_command(label="Clear Recent Projects", command=self.clear_recent_projects)
        
        # Help menu
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Help", menu=help_menu)
        help_menu.add_command(label="About", command=self.show_about)
    

    
    def update_status(self, message):
        """Update status bar message"""
        if hasattr(self, 'status_label'):
            self.status_label.config(text=message)
            self.root.update_idletasks()

    def update_directory_status(self):
        """Update directory status indicator"""
        if hasattr(self, 'directory_status_label'):
            if self.project_directory_accessible:
                self.directory_status_label.config(text="📁 Project Dir: OK", foreground="green")
            else:
                self.directory_status_label.config(text="⚠️ Project Dir: Check Settings", foreground="red")

    def show_directory_notification(self):
        """Show notification if project directory is not accessible"""
        if not self.project_directory_accessible:
            # Schedule the notification to show after UI is fully loaded
            self.root.after(500, self._show_directory_warning)

    def _show_directory_warning(self):
        """Show directory access warning dialog"""
        current_dir = self.config.get("project_directory", get_default_project_directory())

        result = messagebox.askyesno(
            "Project Directory Access Issue",
            f"Cannot access project directory:\n{current_dir}\n\n"
            "This directory is required for saving plotting projects.\n\n"
            "Would you like to select a new directory now?\n\n"
            "(You can also change this later in Settings → Project Directory)",
            icon="warning"
        )

        if result:
            self.change_project_directory_dialog()
    
    def new_data_analysis(self):
        """Start new data analysis workflow"""
        self.update_status("Starting new data analysis...")

        try:
            # Import the ImportWizard
            from ImportWizard import DataImportGUI

            # Hide main window
            self.root.withdraw()

            # Create import wizard window
            import_window = tk.Toplevel(self.root)
            import_window.title("Data Import Wizard")
            import_window.geometry("1200x800")

            # Handle window closing to show main window again
            import_window.protocol("WM_DELETE_WINDOW", lambda: self.on_child_window_close(import_window))

            # Create import wizard with callback
            import_wizard = DataImportGUI(master=import_window, on_complete_callback=self.on_import_complete)

            self.update_status("Import wizard opened")

        except Exception as e:
            messagebox.showerror("Error", f"Could not open import wizard: {e}")
            self.update_status("Ready")
            # Show main window again if there was an error
            self.root.deiconify()
    
    def on_child_window_close(self, window):
        """Handle child window closing"""
        window.destroy()
        # Show main window again
        self.root.deiconify()
        self.update_status("Ready")

    def on_import_complete(self, data, config, filename):
        """Callback when import wizard completes"""
        self.update_status("Import completed, launching visualization...")

        try:
            # Store current session data (no temporary files)
            self.current_project = {
                "data": data,
                "config": config,
                "filename": filename,
                "created": datetime.datetime.now().isoformat()
            }

            # Launch plotting window directly (main window stays hidden)
            self.launch_plotting_window(data, config, filename)

        except Exception as e:
            messagebox.showerror("Error", f"Could not launch visualization: {e}")
            self.update_status("Ready")
            # Show main window again if there was an error
            self.root.deiconify()
    

    
    def launch_plotting_window(self, data, config, filename):
        """Launch appropriate plotting window based on data"""
        try:
            from PlottingIntegration import PlottingLauncher

            # Add project directory to config
            enhanced_config = config.copy() if config else {}
            enhanced_config['project_directory'] = self.config.get("project_directory", get_default_project_directory())

            # Get dimension from config if available (new workflow)
            dimension = config.get('dimension') if config else None

            # Detect dimension and launch plotting window
            plotting_app = PlottingLauncher.launch_plotting_window(
                data=data,
                dimension=dimension,  # Use detected dimension from ImportWizard
                title=f"Data Analysis - {Path(filename).stem}",
                config=enhanced_config,
                filename=filename,
                project_callback=self.on_project_action
            )

            # Set up window closing handler for plotting window
            if hasattr(plotting_app, 'master') and plotting_app.master:
                plotting_app.master.protocol("WM_DELETE_WINDOW",
                                            lambda: self.on_plotting_window_close(plotting_app.master))

            self.current_plotting_window = plotting_app
            self.update_status("Plotting window launched")

        except Exception as e:
            messagebox.showerror("Error", f"Could not launch plotting window: {e}")
            # Show main window again if there was an error
            self.root.deiconify()
    
    def on_plotting_window_close(self, window):
        """Handle plotting window closing"""
        window.destroy()
        self.current_plotting_window = None
        # Show main window again
        self.root.deiconify()
        self.update_status("Ready")

    def on_project_action(self, action, data=None):
        """Handle project actions from plotting windows"""
        if action == "save_project":
            self.save_project(data)
        elif action == "export_data":
            self.export_data(data)
        elif action == "processing_update":
            self.log_processing_action(data)
    
    def save_project(self, project_data=None):
        """Save current project to permanent location"""
        if not self.current_project:
            messagebox.showwarning("Warning", "No active project to save")
            return
        
        # Ask user for save location
        save_dir = filedialog.askdirectory(
            title="Select Project Save Location",
            initialdir=self.config.get("default_export_directory", str(Path.home()))
        )
        
        if not save_dir:
            return
        
        try:
            # Create project folder
            project_name = self.current_project["name"].replace("temp_", "")
            project_path = os.path.join(save_dir, project_name)
            os.makedirs(project_path, exist_ok=True)
            
            # Copy data files
            temp_path = self.current_project["temp_path"]
            for file in os.listdir(temp_path):
                shutil.copy2(os.path.join(temp_path, file), project_path)
            
            # Save project metadata
            project_metadata = {
                "name": project_name,
                "created": self.current_project["created"],
                "saved": datetime.datetime.now().isoformat(),
                "config": self.current_project["config"],
                "processing_log": self.current_project["processing_log"],
                "data_files": os.listdir(project_path)
            }
            
            metadata_path = os.path.join(project_path, "project_metadata.json")
            with open(metadata_path, 'w') as f:
                json.dump(project_metadata, f, indent=4)
            
            # Update recent projects
            self.add_to_recent_projects(project_name, project_path)
            
            messagebox.showinfo("Success", f"Project saved to: {project_path}")
            self.update_status(f"Project saved: {project_name}")
            
        except Exception as e:
            messagebox.showerror("Error", f"Could not save project: {e}")
    
    def open_project(self):
        """Open project overview window"""
        if not self.project_directory_accessible:
            messagebox.showerror(
                "Project Directory Error",
                "Project directory is not accessible. Please check Settings → Project Directory."
            )
            return

        # Create project overview window
        ProjectOverviewWindow(self.root, self.config.get("project_directory"), self)
    
    def populate_recent_projects(self):
        """Populate recent projects list"""
        if not hasattr(self, 'recent_tree'):
            return
            
        # Clear existing items
        for item in self.recent_tree.get_children():
            self.recent_tree.delete(item)
        
        # Add recent projects
        for project in self.config.get("recent_projects", []):
            self.recent_tree.insert("", "end", values=(
                project.get("name", "Unknown"),
                project.get("date", "Unknown"),
                project.get("type", "Unknown"),
                project.get("path", "Unknown")
            ))
    
    def add_to_recent_projects(self, name, path):
        """Add project to recent projects list"""
        recent_projects = self.config.get("recent_projects", [])
        
        # Remove if already exists
        recent_projects = [p for p in recent_projects if p.get("path") != path]
        
        # Add to beginning
        recent_projects.insert(0, {
            "name": name,
            "path": path,
            "date": datetime.datetime.now().strftime("%Y-%m-%d %H:%M"),
            "type": "Data Analysis"
        })
        
        # Limit to max recent projects
        max_recent = self.config.get("max_recent_projects", 10)
        recent_projects = recent_projects[:max_recent]
        
        self.config["recent_projects"] = recent_projects
        self.save_config()
        self.populate_recent_projects()
    
    def handle_project_double_click(self, event):
        """Handle double-click on project - check if clicking on name column for rename"""
        selection = self.recent_tree.selection()
        if not selection:
            return

        # Get click position and determine column
        column = self.recent_tree.identify_column(event.x)

        if column == "#1":  # Name column (first column)
            # Start rename operation
            self.rename_selected_project()
        else:
            # Open project (original behavior)
            self.open_selected_project()

    def show_project_context_menu(self, event):
        """Show context menu for project"""
        # Select the item under cursor
        item = self.recent_tree.identify_row(event.y)
        if item:
            self.recent_tree.selection_set(item)
            self.project_context_menu.post(event.x_root, event.y_root)

    def open_selected_project(self):
        """Open the currently selected project"""
        selection = self.recent_tree.selection()
        if not selection:
            return

        item = self.recent_tree.item(selection[0])
        project_path = item["values"][3]

        # Open project metadata file
        metadata_file = os.path.join(project_path, "project_metadata.json")
        if os.path.exists(metadata_file):
            self.open_project_from_path(metadata_file)
        else:
            messagebox.showerror("Error", "Project metadata file not found")

    def remove_selected_project(self):
        """Remove selected project from recent list"""
        selection = self.recent_tree.selection()
        if not selection:
            return

        item = self.recent_tree.item(selection[0])
        project_name = item["values"][0]
        project_path = item["values"][3]

        # Confirm removal
        if messagebox.askyesno("Confirm Removal",
                              f"Remove '{project_name}' from recent projects list?\n\n"
                              f"This will not delete the project files."):
            # Remove from config
            recent_projects = self.config.get("recent_projects", [])
            self.config["recent_projects"] = [
                p for p in recent_projects
                if p.get("path") != project_path
            ]
            self.save_config()
            self.populate_recent_projects()
            self.update_status(f"Removed '{project_name}' from recent projects")

    def rename_selected_project(self):
        """Rename the selected project"""
        selection = self.recent_tree.selection()
        if not selection:
            return

        item = self.recent_tree.item(selection[0])
        current_name = item["values"][0]
        project_path = item["values"][3]

        # Check if project path exists
        if not os.path.exists(project_path):
            messagebox.showerror("Error", "Project directory not found. Cannot rename.")
            return

        # Create rename dialog
        self.show_rename_dialog(current_name, project_path)

    def show_rename_dialog(self, current_name, project_path):
        """Show dialog for renaming project"""
        dialog = tk.Toplevel(self.root)
        dialog.title("Rename Project")
        dialog.geometry("400x150")
        dialog.resizable(False, False)
        dialog.transient(self.root)
        dialog.grab_set()

        # Center the dialog
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (dialog.winfo_width() // 2)
        y = (dialog.winfo_screenheight() // 2) - (dialog.winfo_height() // 2)
        dialog.geometry(f"+{x}+{y}")

        # Main frame
        main_frame = ttk.Frame(dialog, padding="20")
        main_frame.pack(fill="both", expand=True)

        # Current name label
        ttk.Label(main_frame, text=f"Current name: {current_name}").pack(anchor="w", pady=(0, 10))

        # New name entry
        ttk.Label(main_frame, text="New name:").pack(anchor="w")
        name_var = tk.StringVar(value=current_name)
        name_entry = ttk.Entry(main_frame, textvariable=name_var, width=40)
        name_entry.pack(fill="x", pady=(5, 15))
        name_entry.select_range(0, tk.END)
        name_entry.focus()

        # Buttons frame
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill="x")

        def on_rename():
            new_name = name_var.get().strip()
            if not new_name:
                messagebox.showerror("Error", "Project name cannot be empty")
                return

            if new_name == current_name:
                dialog.destroy()
                return

            # Validate name (no invalid characters for folder names)
            invalid_chars = '<>:"/\\|?*'
            if any(char in new_name for char in invalid_chars):
                messagebox.showerror("Error", f"Project name cannot contain: {invalid_chars}")
                return

            # Perform the rename
            if self.perform_project_rename(current_name, new_name, project_path):
                dialog.destroy()

        def on_cancel():
            dialog.destroy()

        ttk.Button(button_frame, text="Rename", command=on_rename).pack(side="right", padx=(5, 0))
        ttk.Button(button_frame, text="Cancel", command=on_cancel).pack(side="right")

        # Bind Enter key to rename
        dialog.bind('<Return>', lambda e: on_rename())
        dialog.bind('<Escape>', lambda e: on_cancel())

    def perform_project_rename(self, old_name, new_name, old_project_path):
        """Perform the actual project rename operation"""
        try:
            # Get parent directory
            parent_dir = os.path.dirname(old_project_path)
            new_project_path = os.path.join(parent_dir, new_name)

            # Check if new path already exists
            if os.path.exists(new_project_path):
                messagebox.showerror("Error", f"A project named '{new_name}' already exists in this location")
                return False

            # Rename the project directory
            os.rename(old_project_path, new_project_path)

            # Update project metadata file
            metadata_file = os.path.join(new_project_path, "project_metadata.json")
            if os.path.exists(metadata_file):
                with open(metadata_file, 'r') as f:
                    metadata = json.load(f)

                # Update name in metadata
                metadata["name"] = new_name
                metadata["renamed"] = datetime.datetime.now().isoformat()

                with open(metadata_file, 'w') as f:
                    json.dump(metadata, f, indent=4)

            # Update recent projects list
            recent_projects = self.config.get("recent_projects", [])
            for project in recent_projects:
                if project.get("path") == old_project_path:
                    project["name"] = new_name
                    project["path"] = new_project_path
                    project["date"] = datetime.datetime.now().strftime("%Y-%m-%d %H:%M")
                    break

            # Save config and refresh display
            self.save_config()
            self.populate_recent_projects()

            # Show success message
            self.update_status(f"Project renamed from '{old_name}' to '{new_name}'")
            messagebox.showinfo("Success", f"Project successfully renamed to '{new_name}'")

            return True

        except PermissionError:
            messagebox.showerror("Error", "Permission denied. Cannot rename project.\n"
                               "Make sure the project is not open in another application.")
            return False
        except FileExistsError:
            messagebox.showerror("Error", f"A project named '{new_name}' already exists")
            return False
        except Exception as e:
            messagebox.showerror("Error", f"Failed to rename project: {str(e)}")
            return False

    def open_project_from_path(self, metadata_file):
        """Open project from specific path"""
        try:
            # Hide main window
            self.root.withdraw()

            with open(metadata_file, 'r') as f:
                project_metadata = json.load(f)

            project_dir = os.path.dirname(metadata_file)
            data_file = os.path.join(project_dir, "temp_data.csv")

            if os.path.exists(data_file):
                import pandas as pd
                data = pd.read_csv(data_file)

                self.launch_plotting_window(data, project_metadata["config"], project_metadata["name"])
                self.update_status(f"Project opened: {project_metadata['name']}")
            else:
                messagebox.showerror("Error", "Project data file not found")
                # Show main window again if there was an error
                self.root.deiconify()

        except Exception as e:
            messagebox.showerror("Error", f"Could not open project: {e}")
            # Show main window again if there was an error
            self.root.deiconify()
    
    def log_processing_action(self, action_data):
        """Log processing action to current project"""
        if self.current_project:
            self.current_project["processing_log"].append({
                "timestamp": datetime.datetime.now().isoformat(),
                "action": action_data
            })
    
    def clear_recent_projects(self):
        """Clear recent projects list"""
        self.config["recent_projects"] = []
        self.save_config()
        self.populate_recent_projects()
        messagebox.showinfo("Success", "Recent projects cleared")
    

    
    def open_settings(self):
        """Open settings dialog"""
        dialog = tk.Toplevel(self.root)
        dialog.title("Settings")
        dialog.geometry("600x500")
        dialog.resizable(True, True)
        dialog.transient(self.root)
        dialog.grab_set()

        # Center the dialog
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (dialog.winfo_width() // 2)
        y = (dialog.winfo_screenheight() // 2) - (dialog.winfo_height() // 2)
        dialog.geometry(f"+{x}+{y}")

        # Create notebook for tabs
        notebook = ttk.Notebook(dialog)
        notebook.pack(fill="both", expand=True, padx=10, pady=10)

        # Project Directory tab
        self.create_project_directory_tab(notebook)

        # General Settings tab
        self.create_general_settings_tab(notebook)

        # Button frame
        button_frame = ttk.Frame(dialog)
        button_frame.pack(fill="x", padx=10, pady=(0, 10))

        def on_close():
            dialog.destroy()

        ttk.Button(button_frame, text="Close", command=on_close).pack(side="right")

    def create_project_directory_tab(self, notebook):
        """Create project directory settings tab"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="Project Directory")

        # Main content frame with padding
        content_frame = ttk.Frame(frame, padding="20")
        content_frame.pack(fill="both", expand=True)

        # Current directory status
        status_frame = ttk.LabelFrame(content_frame, text="Current Status", padding="10")
        status_frame.pack(fill="x", pady=(0, 20))

        current_dir = self.config.get("project_directory", get_default_project_directory())

        # Directory path
        ttk.Label(status_frame, text="Project Directory:").pack(anchor="w")
        dir_label = ttk.Label(status_frame, text=current_dir, font=("Courier", 9),
                             foreground="blue", wraplength=500)
        dir_label.pack(anchor="w", pady=(5, 10))

        # Access status
        if self.project_directory_accessible:
            status_text = "✅ Directory is accessible and writable"
            status_color = "green"
        else:
            status_text = "❌ Directory is not accessible or not writable"
            status_color = "red"

        status_label = ttk.Label(status_frame, text=status_text, foreground=status_color)
        status_label.pack(anchor="w")

        # Directory management
        management_frame = ttk.LabelFrame(content_frame, text="Directory Management", padding="10")
        management_frame.pack(fill="x", pady=(0, 20))

        # Change directory button
        def change_directory():
            self.change_project_directory_dialog()
            # Close current settings dialog and reopen to refresh
            notebook.master.destroy()
            self.open_settings()

        ttk.Button(management_frame, text="Change Project Directory",
                  command=change_directory).pack(anchor="w", pady=5)

        # Test directory button
        def test_directory():
            current_dir = self.config.get("project_directory", get_default_project_directory())
            if check_directory_permissions(current_dir):
                messagebox.showinfo("Test Result", "✅ Directory is accessible and writable")
            else:
                messagebox.showerror("Test Result", "❌ Directory is not accessible or not writable")

        ttk.Button(management_frame, text="Test Current Directory",
                  command=test_directory).pack(anchor="w", pady=5)

        # Information
        info_frame = ttk.LabelFrame(content_frame, text="Information", padding="10")
        info_frame.pack(fill="both", expand=True)

        info_text = """The project directory is where all your plotting projects will be saved.

Requirements:
• The directory must exist or be creatable
• You must have read and write permissions
• The directory should be accessible across sessions

If the directory becomes inaccessible, you will see a warning in the status bar and should change the directory through this settings dialog."""

        info_label = ttk.Label(info_frame, text=info_text, wraplength=500, justify="left")
        info_label.pack(anchor="w")

    def create_general_settings_tab(self, notebook):
        """Create general settings tab"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="General")

        content_frame = ttk.Frame(frame, padding="20")
        content_frame.pack(fill="both", expand=True)

        ttk.Label(content_frame, text="General settings will be implemented here").pack()

    def change_project_directory_dialog(self):
        """Show dialog to change project directory"""
        current_dir = self.config.get("project_directory", get_default_project_directory())

        # Show current directory info
        if not self.project_directory_accessible:
            messagebox.showwarning(
                "Directory Access Issue",
                f"Current project directory is not accessible:\n{current_dir}\n\n"
                "Please select a new directory where you have read/write permissions."
            )
        else:
            result = messagebox.askyesno(
                "Change Project Directory",
                f"Current project directory:\n{current_dir}\n\n"
                "Do you want to select a new project directory?"
            )
            if not result:
                return

        # Directory selection loop
        while True:
            new_dir = filedialog.askdirectory(
                title="Select Project Directory",
                initialdir=current_dir
            )

            if not new_dir:  # User cancelled
                return

            # Test the selected directory
            if check_directory_permissions(new_dir):
                # Save the new directory to config
                self.config["project_directory"] = new_dir
                self.save_config()
                self.project_directory_accessible = True
                self.update_directory_status()

                messagebox.showinfo(
                    "Directory Changed",
                    f"Project directory changed to:\n{new_dir}\n\n"
                    "This change has been saved."
                )
                return
            else:
                retry = messagebox.askyesno(
                    "Permission Denied",
                    f"Cannot write to selected directory:\n{new_dir}\n\n"
                    "Please select a directory where you have read/write permissions.\n\n"
                    "Try again?"
                )
                if not retry:
                    return
    
    def show_about(self):
        """Show about dialog"""
        about_text = """Data Analysis Application v1.0
        
A comprehensive tool for data import, visualization, processing, and analysis.

Features:
• Data import wizard with multiple format support
• Interactive 2D and 3D plotting
• Advanced data processing tools
• Project management with auto-save
• Export capabilities

© 2024 Data Analysis Team"""
        
        messagebox.showinfo("About", about_text)
    
    def on_closing(self):
        """Handle application closing"""
        try:
            # Save configuration
            self.save_config()

        except Exception as e:
            print(f"Error during cleanup: {e}")

        self.root.destroy()


class ProjectOverviewWindow:
    """Project Overview Window with tree view and preview functionality"""

    def __init__(self, parent, project_directory, main_app):
        self.parent = parent
        self.project_directory = project_directory
        self.main_app = main_app
        self.selected_items = set()  # Track checked items
        self.current_preview_item = None

        # Create window
        self.window = tk.Toplevel(parent)
        self.window.title("Project Overview")
        self.window.geometry("1200x800")
        self.window.resizable(True, True)

        # Make window transient but not modal (remove grab_set to allow other windows)
        self.window.transient(parent)

        # Handle window closing to show main app
        self.window.protocol("WM_DELETE_WINDOW", self.on_closing)

        # Center window
        self.center_window()

        # Setup UI
        self.setup_ui()

        # Load projects
        self.load_projects()

    def center_window(self):
        """Center the window on screen"""
        self.window.update_idletasks()
        x = (self.window.winfo_screenwidth() // 2) - (self.window.winfo_width() // 2)
        y = (self.window.winfo_screenheight() // 2) - (self.window.winfo_height() // 2)
        self.window.geometry(f"+{x}+{y}")

    def setup_ui(self):
        """Setup the user interface"""
        # Main container
        main_frame = ttk.Frame(self.window)
        main_frame.pack(fill="both", expand=True, padx=10, pady=10)

        # Create paned window for left/right split
        paned_window = ttk.PanedWindow(main_frame, orient="horizontal")
        paned_window.pack(fill="both", expand=True)

        # Left panel for tree view and buttons
        left_frame = ttk.Frame(paned_window)
        paned_window.add(left_frame, weight=1)

        # Right panel for preview and comments
        right_frame = ttk.Frame(paned_window)
        paned_window.add(right_frame, weight=1)

        # Setup left panel
        self.setup_left_panel(left_frame)

        # Setup right panel
        self.setup_right_panel(right_frame)

    def setup_left_panel(self, parent):
        """Setup left panel with tree view and action buttons"""
        # Tree view frame
        tree_frame = ttk.LabelFrame(parent, text="Projects and Measurements", padding="5")
        tree_frame.pack(fill="both", expand=True, pady=(0, 10))

        # Create tree view with scrollbars
        tree_container = ttk.Frame(tree_frame)
        tree_container.pack(fill="both", expand=True)

        # Tree view with checkbox column
        self.tree = ttk.Treeview(tree_container, columns=("checkbox", "type", "date"), show="tree headings", selectmode="browse")
        self.tree.heading("#0", text="Name")
        self.tree.heading("checkbox", text="☐")
        self.tree.heading("type", text="Type")
        self.tree.heading("date", text="Modified")

        self.tree.column("#0", width=250)
        self.tree.column("checkbox", width=30)
        self.tree.column("type", width=100)
        self.tree.column("date", width=150)

        # Scrollbars for tree
        v_scrollbar = ttk.Scrollbar(tree_container, orient="vertical", command=self.tree.yview)
        h_scrollbar = ttk.Scrollbar(tree_container, orient="horizontal", command=self.tree.xview)
        self.tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        # Pack tree and scrollbars
        self.tree.grid(row=0, column=0, sticky="nsew")
        v_scrollbar.grid(row=0, column=1, sticky="ns")
        h_scrollbar.grid(row=1, column=0, sticky="ew")

        tree_container.grid_rowconfigure(0, weight=1)
        tree_container.grid_columnconfigure(0, weight=1)

        # Bind tree events
        self.tree.bind("<Button-1>", self.on_tree_click)
        self.tree.bind("<<TreeviewSelect>>", self.on_tree_select)

        # Action buttons frame
        button_frame = ttk.Frame(parent)
        button_frame.pack(fill="x", pady=(5, 0))

        # Action buttons
        ttk.Button(button_frame, text="Merge", command=self.merge_selected).pack(side="left", padx=(0, 5))
        ttk.Button(button_frame, text="Delete", command=self.delete_selected).pack(side="left", padx=(0, 5))
        ttk.Button(button_frame, text="PDF Preview", command=self.pdf_preview_selected).pack(side="left", padx=(0, 5))
        ttk.Button(button_frame, text="Export", command=self.export_selected).pack(side="left", padx=(0, 5))

        # Close button
        ttk.Button(button_frame, text="Close", command=self.window.destroy).pack(side="right")

    def setup_right_panel(self, parent):
        """Setup right panel with preview and comments"""
        # Preview frame
        preview_frame = ttk.LabelFrame(parent, text="Preview", padding="5")
        preview_frame.pack(fill="both", expand=True, pady=(0, 10))

        # Preview canvas with scrollbars
        canvas_frame = ttk.Frame(preview_frame)
        canvas_frame.pack(fill="both", expand=True)

        self.preview_canvas = tk.Canvas(canvas_frame, bg="white", relief="sunken", bd=1)

        # Scrollbars for canvas
        v_scroll_preview = ttk.Scrollbar(canvas_frame, orient="vertical", command=self.preview_canvas.yview)
        h_scroll_preview = ttk.Scrollbar(canvas_frame, orient="horizontal", command=self.preview_canvas.xview)
        self.preview_canvas.configure(yscrollcommand=v_scroll_preview.set, xscrollcommand=h_scroll_preview.set)

        # Pack canvas and scrollbars
        self.preview_canvas.grid(row=0, column=0, sticky="nsew")
        v_scroll_preview.grid(row=0, column=1, sticky="ns")
        h_scroll_preview.grid(row=1, column=0, sticky="ew")

        canvas_frame.grid_rowconfigure(0, weight=1)
        canvas_frame.grid_columnconfigure(0, weight=1)

        # Comments frame
        comments_frame = ttk.LabelFrame(parent, text="Comments", padding="5")
        comments_frame.pack(fill="x", pady=(0, 10))

        # Comments text widget with scrollbar
        comment_container = ttk.Frame(comments_frame)
        comment_container.pack(fill="both", expand=True)

        self.comment_text = tk.Text(comment_container, height=6, wrap="word")
        comment_scroll = ttk.Scrollbar(comment_container, orient="vertical", command=self.comment_text.yview)
        self.comment_text.configure(yscrollcommand=comment_scroll.set)

        self.comment_text.pack(side="left", fill="both", expand=True)
        comment_scroll.pack(side="right", fill="y")

        # Edit button frame
        edit_frame = ttk.Frame(parent)
        edit_frame.pack(fill="x")

        ttk.Button(edit_frame, text="Edit", command=self.edit_selected).pack(side="left")
        ttk.Button(edit_frame, text="Save Comment", command=self.save_comment).pack(side="left", padx=(5, 0))

    def load_projects(self):
        """Load projects from project directory"""
        if not os.path.exists(self.project_directory):
            return

        try:
            for project_name in os.listdir(self.project_directory):
                project_path = os.path.join(self.project_directory, project_name)

                if os.path.isdir(project_path):
                    # Add project node
                    project_item = self.tree.insert("", "end", text=project_name,
                                                   values=("☐", "Project", self.get_modification_date(project_path)))

                    # Add measurement nodes
                    for measurement_name in os.listdir(project_path):
                        measurement_path = os.path.join(project_path, measurement_name)

                        if os.path.isdir(measurement_path):
                            # Check if it has plot_config.json
                            config_file = os.path.join(measurement_path, "plot_config.json")
                            if os.path.exists(config_file):
                                self.tree.insert(project_item, "end", text=measurement_name,
                                               values=("☐", "Measurement", self.get_modification_date(measurement_path)))

                    # Expand project nodes
                    self.tree.item(project_item, open=True)

        except Exception as e:
            messagebox.showerror("Error", f"Could not load projects: {e}")

    def get_modification_date(self, path):
        """Get modification date of path"""
        try:
            timestamp = os.path.getmtime(path)
            return datetime.datetime.fromtimestamp(timestamp).strftime("%Y-%m-%d %H:%M")
        except:
            return "Unknown"

    def on_tree_click(self, event):
        """Handle tree click events"""
        region = self.tree.identify_region(event.x, event.y)
        column = self.tree.identify("column", event.x, event.y)
        item = self.tree.identify("item", event.x, event.y)

        # Debug output
        print(f"TreeClick: region={region}, column={column}, item={item}")
        if item:
            print(f"Item text: {self.tree.item(item, 'text')}")

        if item and column == "#1":  # Checkbox column (first column after #0)
            print(f"Toggling checkbox for: {self.tree.item(item, 'text')}")
            self.toggle_checkbox(item)
        elif item and region == "cell":
            # Load preview for the clicked item
            self.load_preview(item)

    def on_tree_select(self, event):
        """Handle tree selection events"""
        selection = self.tree.selection()
        if selection:
            self.load_preview(selection[0])

    def toggle_checkbox(self, item):
        """Toggle checkbox for item"""
        current_values = list(self.tree.item(item, "values"))

        if item in self.selected_items:
            # Uncheck
            self.selected_items.remove(item)
            current_values[0] = "☐"
        else:
            # Check
            self.selected_items.add(item)
            current_values[0] = "☑"

        self.tree.item(item, values=current_values)

    def load_preview(self, item):
        """Load preview for selected item"""
        self.current_preview_item = item

        # Clear previous preview
        self.preview_canvas.delete("all")
        self.comment_text.delete(1.0, tk.END)

        # Get item path
        item_path = self.get_item_path(item)
        if not item_path:
            return

        # Load preview image
        preview_file = os.path.join(item_path, f"{os.path.basename(item_path)}_preview.png")
        if os.path.exists(preview_file):
            try:
                if PIL_AVAILABLE:
                    from PIL import Image, ImageTk

                    # Load and display image
                    image = Image.open(preview_file)
                    # Resize if too large
                    max_size = (400, 300)
                    image.thumbnail(max_size, Image.Resampling.LANCZOS)

                    self.preview_photo = ImageTk.PhotoImage(image)
                    self.preview_canvas.create_image(10, 10, anchor="nw", image=self.preview_photo)

                    # Update canvas scroll region
                    self.preview_canvas.configure(scrollregion=self.preview_canvas.bbox("all"))
                else:
                    self.preview_canvas.create_text(10, 10, anchor="nw",
                                                  text="PIL not available - cannot display image")

            except Exception as e:
                self.preview_canvas.create_text(10, 10, anchor="nw",
                                              text=f"Could not load preview: {e}")
        else:
            self.preview_canvas.create_text(10, 10, anchor="nw",
                                          text="No preview available")

        # Load comments
        self.load_comments(item_path)

    def get_item_path(self, item):
        """Get filesystem path for tree item"""
        # Build path from tree hierarchy
        path_parts = []
        current = item

        while current:
            path_parts.insert(0, self.tree.item(current, "text"))
            current = self.tree.parent(current)

        if len(path_parts) >= 1:
            return os.path.join(self.project_directory, *path_parts)
        return None

    def load_comments(self, item_path):
        """Load comments from plot_config.json"""
        config_file = os.path.join(item_path, "plot_config.json")
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r') as f:
                    config = json.load(f)

                comment = config.get("comment", "")
                self.comment_text.insert(1.0, comment)

            except Exception as e:
                self.comment_text.insert(1.0, f"Error loading comment: {e}")

    def save_comment(self):
        """Save comment to plot_config.json"""
        if not self.current_preview_item:
            messagebox.showwarning("Warning", "No item selected")
            return

        item_path = self.get_item_path(self.current_preview_item)
        if not item_path:
            return

        config_file = os.path.join(item_path, "plot_config.json")

        try:
            # Load existing config or create new
            config = {}
            if os.path.exists(config_file):
                with open(config_file, 'r') as f:
                    config = json.load(f)

            # Update comment
            config["comment"] = self.comment_text.get(1.0, tk.END).strip()

            # Save config
            with open(config_file, 'w') as f:
                json.dump(config, f, indent=4)

            messagebox.showinfo("Success", "Comment saved")

        except Exception as e:
            messagebox.showerror("Error", f"Could not save comment: {e}")

    def detect_data_dimensionality(self, data_file):
        """Detect if data is 2D or 3D based on column structure"""
        try:
            import pandas as pd
            data = pd.read_csv(data_file)

            # Check number of columns
            if len(data.columns) >= 3:
                # Check if we have X, Y, Z-like columns or multiple Y columns
                columns = [col.lower() for col in data.columns]

                # Look for Z column or 3D indicators
                z_indicators = ['z', 'height', 'depth', 'amplitude', 'intensity']
                has_z = any(indicator in col for col in columns for indicator in z_indicators)

                if has_z or len(data.columns) > 3:
                    return "3D"

            return "2D"

        except Exception as e:
            print(f"Error detecting dimensionality: {e}")
            return "2D"  # Default to 2D on error

    def edit_selected(self):
        """Edit selected measurement in plotting app"""
        if not self.current_preview_item:
            messagebox.showwarning("Warning", "No item selected")
            return

        item_path = self.get_item_path(self.current_preview_item)
        if not item_path:
            return

        # Check if it's a measurement (has plot_config.json)
        config_file = os.path.join(item_path, "plot_config.json")
        if not os.path.exists(config_file):
            messagebox.showwarning("Warning", "Selected item is not a measurement")
            return

        try:
            # Load plot config
            with open(config_file, 'r') as f:
                plot_config = json.load(f)

            # Load data
            data_dir = os.path.join(item_path, "Data")
            data_files = []
            if os.path.exists(data_dir):
                data_files = [f for f in os.listdir(data_dir) if f.endswith('.csv')]

            if not data_files:
                messagebox.showerror("Error", "No data files found in measurement")
                return

            # Load first data file (or let user choose if multiple)
            data_file = os.path.join(data_dir, data_files[0])

            import pandas as pd
            data = pd.read_csv(data_file)

            # Launch plotting window
            from PlottingIntegration import PlottingLauncher

            # Add project directory to config
            enhanced_config = plot_config.copy()
            enhanced_config['project_directory'] = self.project_directory

            plotting_app = PlottingLauncher.launch_plotting_window(
                data=data,
                dimension=None,  # Auto-detect
                title=f"Edit - {self.tree.item(self.current_preview_item, 'text')}",
                config=enhanced_config,
                filename=data_files[0],
                project_callback=self.main_app.on_project_action
            )

            # Hide this window and main app while editing
            self.window.withdraw()
            if self.main_app and hasattr(self.main_app, 'root'):
                self.main_app.root.withdraw()

            # Set up window closing handler
            if hasattr(plotting_app, 'master') and plotting_app.master:
                plotting_app.master.protocol("WM_DELETE_WINDOW",
                                            lambda: self.on_edit_window_close(plotting_app.master))

        except Exception as e:
            messagebox.showerror("Error", f"Could not open for editing: {e}")

    def on_edit_window_close(self, window):
        """Handle edit window closing"""
        window.destroy()
        # Show this window and main app again
        self.window.deiconify()
        if self.main_app and hasattr(self.main_app, 'root'):
            self.main_app.root.deiconify()
        # Refresh the preview in case changes were made
        if self.current_preview_item:
            self.load_preview(self.current_preview_item)

    def merge_selected(self):
        """Merge selected measurements"""
        if len(self.selected_items) < 2:
            messagebox.showwarning("Warning", "Please select at least 2 measurements to merge")
            return

        messagebox.showinfo("Info", "Merge functionality will be implemented in future version")

    def delete_selected(self):
        """Delete selected items"""
        if not self.selected_items:
            messagebox.showwarning("Warning", "No items selected")
            return

        # Confirm deletion
        count = len(self.selected_items)
        if not messagebox.askyesno("Confirm Deletion",
                                  f"Are you sure you want to delete {count} selected item(s)?\n\n"
                                  "This action cannot be undone."):
            return

        try:
            for item in list(self.selected_items):
                item_path = self.get_item_path(item)
                if item_path and os.path.exists(item_path):
                    shutil.rmtree(item_path)

                    # Remove from tree
                    self.tree.delete(item)
                    self.selected_items.remove(item)

            messagebox.showinfo("Success", f"Deleted {count} item(s)")

            # Clear preview if current item was deleted
            if self.current_preview_item not in [item for item in self.tree.get_children("")]:
                self.preview_canvas.delete("all")
                self.comment_text.delete(1.0, tk.END)
                self.current_preview_item = None

        except Exception as e:
            messagebox.showerror("Error", f"Could not delete items: {e}")

    def pdf_preview_selected(self):
        """Open PDF Preview Canvas with selected measurements"""
        if not self.selected_items:
            messagebox.showwarning("Warning", "No items selected")
            return

        # Get paths of selected measurements
        selected_paths = []
        for item in self.selected_items:
            item_path = self.get_item_path(item)
            if item_path and os.path.exists(item_path):
                # Check if it's a measurement (has preview image)
                preview_file = None
                folder_name = os.path.basename(item_path)

                # Look for preview image
                for ext in ['png', 'PNG']:
                    potential_preview = os.path.join(item_path, f"{folder_name}_preview.{ext}")
                    if os.path.exists(potential_preview):
                        preview_file = potential_preview
                        break

                if preview_file:
                    selected_paths.append(preview_file)
                else:
                    print(f"Warning: No preview found for {folder_name}")

        if not selected_paths:
            messagebox.showwarning("Warning", "No preview images found for selected measurements")
            return

        try:
            # Import and launch PDF Preview Canvas
            from PDFPreviewCanvas import PDFPreviewCanvas

            # Create new window for PDF preview
            pdf_window = tk.Toplevel(self.window)
            pdf_window.title("PDF Preview Canvas - Selected Measurements")
            pdf_window.geometry("1400x900")

            # Create PDF preview canvas with selected images
            pdf_canvas = PDFPreviewCanvas(pdf_window, project_overview=self)

            # Load the selected preview images
            pdf_canvas.add_images_to_canvas(selected_paths)

            # Hide this project overview window
            self.window.withdraw()

            # Set up window closing handler
            def on_pdf_window_close():
                pdf_window.destroy()
                self.window.deiconify()  # Show project overview window again

            pdf_window.protocol("WM_DELETE_WINDOW", on_pdf_window_close)

        except Exception as e:
            messagebox.showerror("Error", f"Could not open PDF Preview: {e}")
            import traceback
            traceback.print_exc()

    def export_selected(self):
        """Export selected items to various formats"""
        if not self.selected_items:
            messagebox.showwarning("Warning", "No items selected")
            return

        # Create export options dialog
        export_window = tk.Toplevel(self.window)
        export_window.title("Export Options")
        export_window.geometry("300x200")
        export_window.transient(self.window)
        export_window.grab_set()

        # Center the window
        export_window.update_idletasks()
        x = (export_window.winfo_screenwidth() // 2) - (export_window.winfo_width() // 2)
        y = (export_window.winfo_screenheight() // 2) - (export_window.winfo_height() // 2)
        export_window.geometry(f"+{x}+{y}")

        # Export options
        ttk.Label(export_window, text="Select Export Format:", font=("Arial", 12)).pack(pady=20)

        ttk.Button(export_window, text="PDF Preview Canvas",
                  command=lambda: [export_window.destroy(), self.pdf_preview_selected()]).pack(pady=5)

        ttk.Button(export_window, text="Data Files (CSV)",
                  command=lambda: [export_window.destroy(), self.export_data_files()]).pack(pady=5)

        ttk.Button(export_window, text="Cancel",
                  command=export_window.destroy).pack(pady=10)

    def export_data_files(self):
        """Export data files from selected measurements"""
        if not self.selected_items:
            return

        # Ask user for export directory
        export_dir = filedialog.askdirectory(title="Select Export Directory")
        if not export_dir:
            return

        try:
            exported_count = 0
            for item in self.selected_items:
                item_path = self.get_item_path(item)
                if item_path and os.path.exists(item_path):
                    data_dir = os.path.join(item_path, "Data")
                    if os.path.exists(data_dir):
                        # Copy all data files
                        folder_name = os.path.basename(item_path)
                        target_dir = os.path.join(export_dir, folder_name)
                        os.makedirs(target_dir, exist_ok=True)

                        for file in os.listdir(data_dir):
                            if file.endswith('.csv'):
                                shutil.copy2(os.path.join(data_dir, file),
                                           os.path.join(target_dir, file))
                                exported_count += 1

            messagebox.showinfo("Export Complete", f"Exported {exported_count} data files to {export_dir}")

        except Exception as e:
            messagebox.showerror("Export Error", f"Could not export data files: {e}")

    def on_closing(self):
        """Handle window closing"""
        # Show main app window if it exists
        if self.main_app and hasattr(self.main_app, 'root'):
            self.main_app.root.deiconify()

        self.window.destroy()


def main():
    """Main function to run the Data Analysis Application"""
    root = tk.Tk()
    app = DataAnalysisApp(root)
    
    # Handle window closing
    root.protocol("WM_DELETE_WINDOW", app.on_closing)
    
    root.mainloop()

if __name__ == "__main__":
    main()
