#!/usr/bin/env python3
"""
Test script to reproduce the 'list object has no to_csv' error
"""

import pandas as pd
import numpy as np
import sys
import os
import traceback

# Add current directory to path
sys.path.insert(0, os.getcwd())

def test_import_wizard_workflow():
    """Test the complete ImportWizard workflow to reproduce the error"""
    print("=== Testing ImportWizard Workflow ===")
    
    try:
        # Import the ImportWizard
        from ImportWizard import ImportWizard
        import tkinter as tk
        
        # Create a test callback to capture the data
        captured_data = {}
        
        def test_callback(data, config, filename):
            captured_data['data'] = data
            captured_data['config'] = config
            captured_data['filename'] = filename
            print(f"Callback received:")
            print(f"  Data type: {type(data)}")
            if isinstance(data, list):
                print(f"  Data is list with {len(data)} items")
                for i, item in enumerate(data):
                    print(f"    Item {i}: {type(item)} - {item.shape if hasattr(item, 'shape') else 'no shape'}")
            else:
                print(f"  Data shape: {data.shape if hasattr(data, 'shape') else 'no shape'}")
            print(f"  Config keys: {list(config.keys())}")
            print(f"  Multi-dataset: {config.get('multi_dataset', False)}")
        
        # Create root window
        root = tk.Tk()
        root.withdraw()  # Hide it
        
        # Create ImportWizard window
        wizard_window = tk.Toplevel()
        wizard_window.withdraw()  # Hide it too
        
        # Create ImportWizard instance
        wizard = ImportWizard(wizard_window, test_callback)
        
        # Load test file
        test_file = "test_data/multi_2d_data.csv"
        if not os.path.exists(test_file):
            print(f"✗ Test file {test_file} not found")
            return False
        
        # Simulate the import process
        wizard.current_file = test_file
        wizard.load_file()
        
        print(f"✓ File loaded: {wizard.current_file}")
        print(f"✓ Data shape: {wizard.data.shape}")
        print(f"✓ Columns: {list(wizard.data.columns)}")
        
        # Simulate proceeding through the wizard
        # This should trigger the data processing and callback
        
        # Clean up
        wizard_window.destroy()
        root.destroy()
        
        # Check what was captured
        if captured_data:
            print("✓ Callback was triggered successfully")
            return True
        else:
            print("⚠ Callback was not triggered")
            return False
        
    except Exception as e:
        print(f"✗ ImportWizard test failed: {str(e)}")
        traceback.print_exc()
        return False

def test_plotting_app_save():
    """Test PlottingApp save operation to reproduce to_csv error"""
    print("\n=== Testing PlottingApp Save Operation ===")
    
    try:
        # Create test data
        x_data = np.linspace(0, 10, 50)
        
        # Create multiple datasets
        datasets = []
        for i, func_name in enumerate(['sin', 'cos', 'tan']):
            if func_name == 'sin':
                y_data = np.sin(x_data)
            elif func_name == 'cos':
                y_data = np.cos(x_data)
            else:
                y_data = np.tan(x_data)
                
            df = pd.DataFrame({
                'X': x_data,
                'Y': y_data
            })
            df.name = f"{func_name}_wave"
            datasets.append(df)
        
        # Create config
        config = {
            "title": "Test Save Operation",
            "dimension": 2,
            "multi_dataset": True,
            "total_datasets": len(datasets),
            "dataset_names": [df.name for df in datasets]
        }
        
        print(f"✓ Created {len(datasets)} test datasets")
        
        # Test PlottingApp2D
        import tkinter as tk
        from PlottingApp2D import Plot2DWindow
        
        root = tk.Tk()
        root.withdraw()
        
        plot_window = tk.Toplevel()
        plot_window.withdraw()
        
        # Create the app
        app = Plot2DWindow(plot_window, datasets, config)
        print(f"✓ PlottingApp2D created successfully")
        
        # Try to trigger a save operation that might cause the error
        # Check if measurements were created properly
        print(f"✓ Measurements created: {len(app.measurements)}")
        
        for i, measurement in enumerate(app.measurements):
            print(f"  Measurement {i}: {measurement.name}")
            print(f"    Type: {type(measurement)}")
            print(f"    X data type: {type(measurement.x)}")
            print(f"    Y signals type: {type(measurement.y_signals)}")
            
            # Try to call to_dataframe() which might be where the error occurs
            try:
                df = measurement.to_dataframe()
                print(f"    to_dataframe() successful: {df.shape}")
                
                # Try to call to_csv on the result
                import tempfile
                with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
                    df.to_csv(f.name, index=False)
                    print(f"    to_csv() successful")
                    os.unlink(f.name)  # Clean up
                    
            except Exception as e:
                print(f"    ✗ Error in to_dataframe() or to_csv(): {str(e)}")
                traceback.print_exc()
                return False
        
        plot_window.destroy()
        root.destroy()
        
        return True
        
    except Exception as e:
        print(f"✗ PlottingApp save test failed: {str(e)}")
        traceback.print_exc()
        return False

def main():
    """Run all tests to reproduce the to_csv error"""
    print("Testing to_csv Error Reproduction")
    print("=" * 40)
    
    success1 = test_import_wizard_workflow()
    success2 = test_plotting_app_save()
    
    print("\n" + "=" * 40)
    if success1 and success2:
        print("✓ All tests passed - no to_csv error found")
    else:
        print("✗ Some tests failed - check output above")
    
    return 0

if __name__ == "__main__":
    exit(main())
