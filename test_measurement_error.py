import pandas as pd
import numpy as np
import sys
import os

# Add the current directory to the path so we can import PlottingApp2D
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PlottingApp2D import Measurement

def test_measurement_creation():
    """Test creating a Measurement object and calling to_dataframe()"""
    print("Testing Measurement creation and to_dataframe()...")
    
    # Create test data
    x_data = np.array([1, 2, 3, 4, 5])
    y_dict = {
        'y1': np.array([10, 20, 30, 40, 50]),
        'y2': np.array([15, 25, 35, 45, 55])
    }
    
    # Create Measurement object
    measurement = Measurement("Test Measurement", x_data, y_dict)
    
    print(f"Measurement created: {measurement}")
    print(f"Measurement name: {measurement.name}")
    print(f"Measurement x type: {type(measurement.x)}")
    print(f"Measurement y_signals type: {type(measurement.y_signals)}")
    print(f"Measurement y_signals keys: {list(measurement.y_signals.keys())}")
    
    # Test to_dataframe()
    try:
        df = measurement.to_dataframe()
        print(f"to_dataframe() successful: {type(df)}")
        print(f"DataFrame shape: {df.shape}")
        print(f"DataFrame columns: {list(df.columns)}")
        print("DataFrame head:")
        print(df.head())
        
        # Test to_csv()
        df.to_csv("test_output.csv", index=False)
        print("to_csv() successful")
        
    except Exception as e:
        print(f"Error in to_dataframe(): {e}")
        import traceback
        traceback.print_exc()

def test_malformed_measurement():
    """Test with potentially malformed data"""
    print("\nTesting with potentially malformed data...")
    
    # Create test data with lists instead of numpy arrays
    x_data = [1, 2, 3, 4, 5]  # List instead of numpy array
    y_dict = {
        'y1': [10, 20, 30, 40, 50],  # List instead of numpy array
        'y2': [15, 25, 35, 45, 55]   # List instead of numpy array
    }
    
    # Create Measurement object
    measurement = Measurement("Test Measurement 2", x_data, y_dict)
    
    print(f"Measurement created with lists: {measurement}")
    print(f"Measurement x type after creation: {type(measurement.x)}")
    print(f"Measurement y_signals type: {type(measurement.y_signals)}")
    
    # Check y_signals structure
    for key, value in measurement.y_signals.items():
        print(f"  {key}: {type(value)} -> data: {type(value.get('data', 'no data'))}")
    
    # Test to_dataframe()
    try:
        df = measurement.to_dataframe()
        print(f"to_dataframe() successful with lists: {type(df)}")
        print(f"DataFrame shape: {df.shape}")
        
    except Exception as e:
        print(f"Error in to_dataframe() with lists: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_measurement_creation()
    test_malformed_measurement()
