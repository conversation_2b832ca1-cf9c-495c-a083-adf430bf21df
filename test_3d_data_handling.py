#!/usr/bin/env python3
"""
Test script to verify 3D data handling is working correctly.
Tests that:
1. Each file creates one complete 3D dataset (one DataFrame)
2. Multi-file import creates a list of DataFrames
3. Config is set correctly for 3D multi-dataset handling
"""

import pandas as pd
import numpy as np
import tempfile
import os
from pathlib import Path

def create_test_3d_files():
    """Create test 3D data files"""
    test_files = []
    
    # Create temporary directory
    temp_dir = tempfile.mkdtemp()
    
    # Create 3 test files with 3D data
    for file_num in range(1, 4):
        # Create 3D data: X, Y, Z1, Z2, Z3 columns
        x_vals = np.linspace(0, 10, 20)
        y_vals = np.linspace(0, 5, 15)
        
        # Create flattened 3D data
        data_rows = []
        for x in x_vals:
            for y in y_vals:
                # Different Z functions for each file
                if file_num == 1:
                    z1 = np.sin(x) * np.cos(y)
                    z2 = np.cos(x) * np.sin(y)
                    z3 = np.sin(x + y)
                elif file_num == 2:
                    z1 = np.exp(-((x-5)**2 + (y-2.5)**2)/10)
                    z2 = np.sin(x/2) * np.cos(y/2)
                    z3 = x * y / 50
                else:  # file_num == 3
                    z1 = np.cos(x) * np.cos(y)
                    z2 = np.sin(x/3) * np.sin(y/3)
                    z3 = (x**2 + y**2) / 100
                
                data_rows.append([x, y, z1, z2, z3])
        
        # Create DataFrame
        df = pd.DataFrame(data_rows, columns=['X', 'Y', 'Z1', 'Z2', 'Z3'])
        
        # Save to file
        file_path = os.path.join(temp_dir, f'test_3d_data_{file_num}.csv')
        df.to_csv(file_path, index=False)
        test_files.append(file_path)
        
        print(f"Created test file {file_num}: {file_path}")
        print(f"  Shape: {df.shape}")
        print(f"  Columns: {list(df.columns)}")
        print(f"  Sample data:\n{df.head(3)}\n")
    
    return test_files, temp_dir

def test_import_wizard_3d_processing():
    """Test the ImportWizard 3D data processing"""
    print("=== Testing ImportWizard 3D Data Processing ===\n")
    
    # Create test files
    test_files, temp_dir = create_test_3d_files()
    
    try:
        # Import the ImportWizard
        from ImportWizard import ImportWizard
        import tkinter as tk
        
        # Create a temporary root window
        root = tk.Tk()
        root.withdraw()  # Hide it
        
        # Create ImportWizard instance
        wizard = ImportWizard(root)
        
        # Set up the files
        wizard.current_files = test_files
        
        # Test single file processing
        print("--- Testing Single File Processing ---")
        wizard.parsed_data = pd.read_csv(test_files[0])
        
        # Set up assignments for 3D flattened data
        assignments = {
            'data_format': 'flattened',
            'x_column': 'X',
            'y_column': 'Y', 
            'z_columns': ['Z1', 'Z2', 'Z3'],
            'x_name': 'X',
            'y_name': 'Y',
            'z_name': 'Z'
        }
        
        # Process single file
        result = wizard._process_3d_data(assignments)
        
        print(f"Single file result type: {type(result)}")
        if isinstance(result, pd.DataFrame):
            print(f"Single file DataFrame shape: {result.shape}")
            print(f"Single file DataFrame columns: {list(result.columns)}")
            print("✓ Single file processing returns one DataFrame")
        else:
            print("✗ Single file processing should return one DataFrame")
        
        # Test multi-file processing
        print("\n--- Testing Multi-File Processing ---")
        
        # Process multiple files
        multi_result = wizard._process_multi_file_data(assignments, '3D')
        
        print(f"Multi-file result type: {type(multi_result)}")
        if isinstance(multi_result, list):
            print(f"Multi-file result length: {len(multi_result)}")
            print("✓ Multi-file processing returns list of DataFrames")
            
            for i, df in enumerate(multi_result):
                print(f"  Dataset {i+1}: {type(df)}, shape: {df.shape}, name: {getattr(df, 'name', 'No name')}")
                
            if len(multi_result) == len(test_files):
                print("✓ Correct number of datasets created")
            else:
                print(f"✗ Expected {len(test_files)} datasets, got {len(multi_result)}")
        else:
            print("✗ Multi-file processing should return list of DataFrames")
        
        # Test config creation
        print("\n--- Testing Config Creation ---")
        
        # Test config for multi-file 3D data
        wizard.processed_data = multi_result
        config = wizard._create_plot_configs(multi_result, 3)
        
        print(f"Config multi_dataset: {config.get('multi_dataset')}")
        print(f"Config total_datasets: {config.get('total_datasets')}")
        print(f"Config dimension: {config.get('dimension')}")
        
        if config.get('multi_dataset') == True:
            print("✓ Config correctly sets multi_dataset=True for 3D list")
        else:
            print("✗ Config should set multi_dataset=True for 3D list")
        
        root.destroy()
        
    except Exception as e:
        print(f"Error during testing: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Clean up test files
        for file_path in test_files:
            try:
                os.remove(file_path)
            except:
                pass
        try:
            os.rmdir(temp_dir)
        except:
            pass

if __name__ == "__main__":
    test_import_wizard_3d_processing()
