import os
import numpy as np
import pandas as pd
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
from pathlib import Path
import json
from typing import Dict, List, Tuple, Any
import re
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg


class HeaderMetadataDetector:
    """Handles detection and management of headers and metadata in files."""

    def __init__(self):
        self.detected_headers = []
        self.detected_metadata = []
        self.data_start_line = 0
        self.data_end_line = -1

    def analyze_file_content(self, file_path: str, delimiter: str = None) -> Dict[str, Any]:
        """Analyze file for headers, metadata, and data boundaries."""

        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            lines = f.readlines()

        # Auto-detect delimiter if not provided
        if delimiter is None:
            delimiter = self._detect_delimiter_simple(lines[:10])

        analysis = {
            'total_lines': len(lines),
            'headers': [],
            'metadata': [],
            'data_start': 0,
            'data_end': len(lines) - 1,
            'delimiter': delimiter
        }

        # First pass: analyze each line
        line_analyses = []
        for i, line in enumerate(lines):
            line_info = self._analyze_line(line.strip(), delimiter, i)
            line_analyses.append(line_info)

        # Find data boundaries
        data_start = -1
        data_end = -1

        # Find first data line
        for i, line_info in enumerate(line_analyses):
            if line_info['type'] == 'data':
                data_start = i
                break

        # Find last data line
        for i in range(len(line_analyses) - 1, -1, -1):
            line_info = line_analyses[i]
            if line_info['type'] == 'data':
                data_end = i
                break

        # Set defaults if no data found
        if data_start == -1:
            data_start = 0
        if data_end == -1:
            data_end = len(lines) - 1

        analysis['data_start'] = data_start
        analysis['data_end'] = data_end

        # Classify lines based on position relative to data
        # Header should ONLY be the single line immediately before data with column names
        header_found = False

        for i, line_info in enumerate(line_analyses):
            if line_info['type'] == 'data':
                # Skip data lines - they shouldn't be in headers/metadata
                continue
            elif i == data_start - 1 and not header_found:
                # Line immediately before data - check if it's a proper header
                parts = line_info['content'].split(delimiter)
                if len(parts) > 1:  # Multiple columns
                    # Check if it's mostly text (column names)
                    text_count = 0
                    for part in parts:
                        part = part.strip()
                        if part:
                            try:
                                float(part.replace(',', '.'))
                            except ValueError:
                                text_count += 1

                    # If mostly text, it's a header
                    if text_count / len([p for p in parts if p.strip()]) > 0.7:
                        line_info['type'] = 'header'
                        line_info['reason'] = 'Column names line immediately before data'
                        line_info['confidence'] = 0.9
                        analysis['headers'].append(line_info)
                        header_found = True
                        continue

                # If not a header, treat as metadata
                line_info['type'] = 'metadata'
                line_info['reason'] = 'Non-header line before data'
                analysis['metadata'].append(line_info)
            else:
                # Everything else is metadata
                line_info['type'] = 'metadata'
                if i < data_start:
                    line_info['reason'] = 'Content before data (metadata)'
                elif i > data_end:
                    line_info['reason'] = 'Content after data (metadata)'
                else:
                    line_info['reason'] = 'Non-data content within data range'
                analysis['metadata'].append(line_info)

        return analysis

    def _detect_delimiter_simple(self, sample_lines: List[str]) -> str:
        """Simple delimiter detection for analysis."""
        delimiters = [',', '\t', ';', '|']  # Removed space as it's too common
        delimiter_counts = {delim: 0 for delim in delimiters}

        # Only count delimiters in lines that look like data (not comments)
        data_lines = [line for line in sample_lines if line.strip() and not line.strip().startswith('#')]

        for line in data_lines:
            if line:
                for delim in delimiters:
                    delimiter_counts[delim] += line.count(delim)

        # Return delimiter with highest count, default to comma
        max_delim = max(delimiter_counts, key=delimiter_counts.get)
        return max_delim if delimiter_counts[max_delim] > 0 else ','

    def _analyze_line(self, line: str, delimiter: str, line_number: int) -> Dict[str, Any]:
        """Analyze a single line to determine its type and content."""

        line_info = {
            'line_number': line_number,
            'content': line,
            'type': 'unknown',
            'confidence': 0.0,
            'reason': ''
        }

        # Skip empty lines
        if not line.strip():
            line_info['type'] = 'empty'
            return line_info

        # Check for obvious comments/metadata markers
        comment_markers = ['#', '//', '/*', '*', '%', ';', '!']
        if any(line.strip().startswith(marker) for marker in comment_markers):
            line_info['type'] = 'metadata'
            line_info['confidence'] = 0.9
            line_info['reason'] = 'Comment marker detected'
            return line_info

        # Check for common metadata patterns
        metadata_patterns = [
            r'^(date|time|created|modified|author|version|description|notes?|comments?):',
            r'^(instrument|device|software|method|protocol):',
            r'^(temperature|pressure|humidity|conditions?):',
            r'^(sample|specimen|material):',
            r'^\w+\s*=\s*\w+',  # key=value pairs
            r'^\[.*\]$',  # [section headers]
            r'^<.*>$',    # <xml-like tags>
        ]

        for pattern in metadata_patterns:
            if re.search(pattern, line, re.IGNORECASE):
                line_info['type'] = 'metadata'
                line_info['confidence'] = 0.8
                line_info['reason'] = f'Metadata pattern: {pattern}'
                return line_info

        # Split line by delimiter and analyze parts
        parts = line.split(delimiter) if delimiter in line else [line]

        if len(parts) > 1:
            # Check if this looks like a header (mostly text, few numbers)
            text_count = 0
            number_count = 0

            for part in parts:
                part = part.strip()
                if not part:
                    continue

                # Try to convert to number
                try:
                    float(part.replace(',', '.'))
                    number_count += 1
                except ValueError:
                    text_count += 1

            total_parts = text_count + number_count
            if total_parts > 0:
                text_ratio = text_count / total_parts

                if text_ratio > 0.7:  # Mostly text
                    line_info['type'] = 'header'
                    line_info['confidence'] = text_ratio
                    line_info['reason'] = f'High text ratio: {text_ratio:.2f}'
                elif text_ratio < 0.3:  # Mostly numbers
                    line_info['type'] = 'data'
                    line_info['confidence'] = 1 - text_ratio
                    line_info['reason'] = f'High numeric ratio: {1-text_ratio:.2f}'
                else:  # Mixed
                    line_info['type'] = 'mixed'
                    line_info['confidence'] = 0.5
                    line_info['reason'] = f'Mixed content: {text_ratio:.2f} text ratio'
        else:
            # Single value - check if it's a number or text
            try:
                float(line.replace(',', '.'))
                line_info['type'] = 'data'
                line_info['confidence'] = 0.7
                line_info['reason'] = 'Single numeric value'
            except ValueError:
                line_info['type'] = 'metadata'
                line_info['confidence'] = 0.6
                line_info['reason'] = 'Single text value'

        return line_info

    def save_headers_metadata(self, file_path: str, analysis: Dict[str, Any],
                             output_dir: str, save_content: bool = True) -> List[str]:
        """Save detected headers and metadata to a single file in the specified directory."""

        saved_files = []
        base_path = Path(file_path)
        base_name = base_path.stem
        output_path = Path(output_dir)

        if save_content and (analysis['headers'] or analysis['metadata']):
            content_file = output_path / f"{base_name}_file_info.txt"
            with open(content_file, 'w', encoding='utf-8') as f:
                f.write(f"File Information for: {base_path.name}\n")
                f.write("=" * 60 + "\n\n")

                # Write headers section
                if analysis['headers']:
                    f.write("COLUMN HEADERS\n")
                    f.write("-" * 20 + "\n")
                    for header in analysis['headers']:
                        f.write(f"Line {header['line_number']}: {header['content']}\n")
                        #f.write(f"  Confidence: {header['confidence']:.2f}\n")
                        #f.write(f"  Reason: {header['reason']}\n\n")
                else:
                    f.write("COLUMN HEADERS\n")
                    f.write("-" * 20 + "\n")
                    f.write("No column headers detected in file.\n\n")

                # Write metadata section
                if analysis['metadata']:
                    f.write("METADATA & COMMENTS\n")
                    f.write("-" * 25 + "\n")
                    for metadata in analysis['metadata']:
                        f.write(f"Line {metadata['line_number']}: {metadata['content']}\n")
                        #f.write(f"  Confidence: {metadata['confidence']:.2f}\n")
                        #f.write(f"  Reason: {metadata['reason']}\n\n")
                else:
                    f.write("METADATA & COMMENTS\n")
                    f.write("-" * 25 + "\n")
                    f.write("No metadata detected in file.\n\n")

                # Write summary
                f.write("SUMMARY\n")
                f.write("-" * 10 + "\n")
                f.write(f"Total lines in file: {analysis['total_lines']}\n")
                f.write(f"Data starts at line: {analysis['data_start']}\n")
                f.write(f"Data ends at line: {analysis['data_end']}\n")
                f.write(f"Headers found: {len(analysis['headers'])}\n")
                f.write(f"Metadata lines found: {len(analysis['metadata'])}\n")
                f.write(f"Delimiter used: '{analysis['delimiter']}'\n")

            saved_files.append(str(content_file))

        return saved_files


class DataDetector:
    """Handles automatic detection of file format parameters."""

    @staticmethod
    def detect_delimiter(file_path: str, sample_lines: int = 10) -> str:
        """Detect column delimiter in the file."""
        delimiters = [',', '\t', ';', '|', ' ']
        delimiter_counts = {delim: 0 for delim in delimiters}

        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            lines = [f.readline().strip() for _ in range(sample_lines) if f.readable()]

        for line in lines:
            if line:
                for delim in delimiters:
                    delimiter_counts[delim] += line.count(delim)

        # Return delimiter with highest consistent count
        return max(delimiter_counts, key=delimiter_counts.get)

    @staticmethod
    def detect_decimal_separator(file_path: str, delimiter: str, sample_lines: int = 20) -> str:
        """Detect decimal separator (dot or comma)."""
        dot_count = 0
        comma_count = 0

        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            lines = [f.readline().strip() for _ in range(sample_lines) if f.readable()]

        for line in lines:
            if line and delimiter in line:
                parts = line.split(delimiter)
                for part in parts:
                    part = part.strip()
                    # Check if it looks like a number
                    if re.match(r'^-?\d+[.,]\d+$', part):
                        if '.' in part:
                            dot_count += 1
                        if ',' in part:
                            comma_count += 1

        return ',' if comma_count > dot_count else '.'

    @staticmethod
    def detect_header_and_data_start(file_path: str, delimiter: str, decimal_sep: str) -> Tuple[int, int]:
        """Detect header line and first data line."""
        header_line = -1
        data_start = 0

        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            lines = f.readlines()

        for i, line in enumerate(lines[:20]):  # Check first 20 lines
            line = line.strip()
            if not line or line.startswith('#'):  # Skip empty lines and comments
                continue

            parts = line.split(delimiter)
            if len(parts) > 1:
                # Check if this line contains mostly numbers
                numeric_count = 0
                for part in parts:
                    part = part.strip().replace(decimal_sep, '.')
                    try:
                        float(part)
                        numeric_count += 1
                    except ValueError:
                        pass

                # If more than half are numbers, likely data
                if numeric_count / len(parts) > 0.5:
                    data_start = i
                    # Only set header if we haven't found data yet and there's a potential header line before
                    if i > 0:
                        # Check if previous non-empty, non-comment line could be header
                        for j in range(i - 1, -1, -1):
                            prev_line = lines[j].strip()
                            if prev_line and not prev_line.startswith('#'):
                                prev_parts = prev_line.split(delimiter)
                                if len(prev_parts) == len(parts):
                                    # Check if previous line is mostly text (potential header)
                                    text_count = 0
                                    for part in prev_parts:
                                        part = part.strip().replace(decimal_sep, '.')
                                        try:
                                            float(part)
                                        except ValueError:
                                            text_count += 1
                                    if text_count / len(prev_parts) > 0.5:
                                        header_line = j
                                break
                    break

        return header_line, data_start


class DataParser:
    """Handles data parsing and organization."""

    def __init__(self):
        self.raw_data = None
        self.column_names = []
        self.data_shape = None
        self.original_data = None  # Store original parsed data for reference

    def parse_file(self, file_path: str, delimiter: str, decimal_sep: str,
                   header_line: int, data_start: int, excluded_rows: List[int] = None,
                   excluded_cols: List[int] = None, file_analysis: Dict[str, Any] = None) -> pd.DataFrame:
        """Parse file with specified parameters."""

        # Use pandas to read file efficiently instead of loading all lines into memory
        # This is much more memory efficient for large files
        try:
            # First, try to read with pandas directly for better performance
            temp_df = pd.read_csv(file_path, sep=delimiter, decimal=decimal_sep,
                                header=None, encoding='utf-8', on_bad_lines='skip')
            lines = None  # We'll work with the DataFrame directly when possible
        except:
            # Fallback to line-by-line reading only if pandas fails
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()
            temp_df = None

        # Extract header if exists
        self.column_names = []
        if temp_df is not None:
            # Using pandas - handle header differently
            if header_line >= 0:
                # Re-read with proper header
                try:
                    temp_df = pd.read_csv(file_path, sep=delimiter, decimal=decimal_sep,
                                        header=header_line, encoding='utf-8', on_bad_lines='skip')
                    self.column_names = list(temp_df.columns)
                except:
                    # Fallback to no header
                    self.column_names = [f"Column_{i}" for i in range(len(temp_df.columns))]
            else:
                self.column_names = [f"Column_{i}" for i in range(len(temp_df.columns))]
        elif lines is not None:
            # Using line-by-line reading
            if header_line >= 0 and header_line < len(lines):
                header = lines[header_line].strip().split(delimiter)
                self.column_names = [col.strip() for col in header if col.strip()]

        # Handle data extraction based on method used
        if temp_df is not None:
            # Using pandas - much more efficient
            # Apply data_start if specified
            if data_start > 0:
                temp_df = temp_df.iloc[data_start:]

            # Apply exclusions if specified
            if excluded_rows:
                temp_df = temp_df.drop(excluded_rows, errors='ignore')
            if excluded_cols:
                temp_df = temp_df.drop(temp_df.columns[excluded_cols], axis=1, errors='ignore')

            # Store the result
            self.raw_data = temp_df.values
            self.data_shape = temp_df.shape

            # Create final DataFrame
            if not self.column_names:
                self.column_names = [f"Column_{i}" for i in range(temp_df.shape[1])]

            # Ensure we have the right number of column names
            while len(self.column_names) < temp_df.shape[1]:
                self.column_names.append(f"Column_{len(self.column_names)}")

            result_df = pd.DataFrame(temp_df.values, columns=self.column_names[:temp_df.shape[1]])
            return result_df

        # Fallback to line-by-line processing only if pandas failed
        excluded_line_numbers = set()
        if file_analysis:
            # Only exclude high-confidence headers and metadata
            for header in file_analysis.get('headers', []):
                if header['confidence'] > 0.7:  # Only exclude high-confidence headers
                    excluded_line_numbers.add(header['line_number'])
            for metadata in file_analysis.get('metadata', []):
                if metadata['confidence'] > 0.8:  # Only exclude high-confidence metadata
                    excluded_line_numbers.add(metadata['line_number'])

        # Extract data lines, excluding detected headers/metadata
        data_rows = []
        max_columns = 0

        for i, line in enumerate(lines):
            # Skip lines that were detected as headers/metadata
            if i in excluded_line_numbers:
                continue

            line = line.strip()
            if line and not line.startswith('#'):  # Skip empty lines and comments
                parts = line.split(delimiter)
                # Convert decimal separator and clean up
                parts = [part.strip().replace(decimal_sep, '.') if decimal_sep != '.' else part.strip()
                         for part in parts if part.strip()]  # Remove empty parts
                if parts:  # Only add non-empty rows
                    # Check if this looks like data (mostly numeric)
                    numeric_count = 0
                    for part in parts:
                        try:
                            float(part)
                            numeric_count += 1
                        except ValueError:
                            pass

                    # Only include if it's mostly numeric (likely data)
                    if len(parts) > 1 and numeric_count / len(parts) > 0.5:
                        data_rows.append(parts)
                        max_columns = max(max_columns, len(parts))

        # Create DataFrame
        if data_rows:
            # Pad shorter rows with NaN to ensure consistent column count
            for row in data_rows:
                while len(row) < max_columns:
                    row.append('')

            df = pd.DataFrame(data_rows)

            # Set column names
            if self.column_names and len(self.column_names) == len(df.columns):
                df.columns = self.column_names
            else:
                # Generate generic column names
                df.columns = [f'Col_{i}' for i in range(len(df.columns))]

            # Convert to numeric where possible
            for col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='ignore')

            # Store original data before exclusions
            self.original_data = df.copy()

            # Apply exclusions based on DISPLAYED indices
            if excluded_rows:
                df = df.drop(df.index[excluded_rows], errors='ignore')

            if excluded_cols:
                cols_to_drop = [df.columns[i] for i in excluded_cols if i < len(df.columns)]
                df = df.drop(columns=cols_to_drop, errors='ignore')

            self.raw_data = df
            self.data_shape = df.shape
            return df

        return pd.DataFrame()

    def get_current_data(self) -> pd.DataFrame:
        """Get current processed data."""
        return self.raw_data if self.raw_data is not None else pd.DataFrame()


class DataOrganizer:
    """Handles data organization and export."""

    def __init__(self, project_dir: str):
        self.project_dir = Path(project_dir)
        self.project_dir.mkdir(parents=True, exist_ok=True)

    def organize_data(self, data: pd.DataFrame, column_assignments: Dict[str, Any],
                      data_type: str, filename: str) -> Dict[str, Any]:
        """Organize data based on column assignments."""

        organized = {
            'data_type': data_type,
            'filename': filename,
            'datasets': {}
        }

        if data_type == '1D':
            if column_assignments.get('subtype') == 'list':
                # Single column or row as list
                source_type = column_assignments.get('source_type', 'column')
                source_index = column_assignments.get('source_index', 0)

                if source_type == 'column':
                    col_name = data.columns[source_index]
                    organized['datasets'][f"{filename}_{col_name}"] = {
                        'Y': data.iloc[:, source_index].values
                    }
                else:  # row
                    organized['datasets'][f"{filename}_row_{source_index}"] = {
                        'Y': data.iloc[source_index, :].values
                    }
            else:  # table
                # Multiple columns as separate datasets
                y_columns = column_assignments.get('y_columns', [])
                x_column = column_assignments.get('x_column')

                for y_col in y_columns:
                    dataset_name = f"{filename}_{y_col}" if len(y_columns) > 1 else filename
                    dataset = {'Y': data[y_col].values}
                    if x_column:
                        dataset['X'] = data[x_column].values
                    organized['datasets'][dataset_name] = dataset

        elif data_type == '2D':
            multiple_measurements = column_assignments.get('multiple_measurements', False)

            if multiple_measurements:
                # Create multiple datasets from different Y columns
                x_column = column_assignments.get('x_column')
                y_columns = column_assignments.get('y_columns', [])

                for y_col in y_columns:
                    dataset = {'Y': data[y_col].values}
                    if x_column:
                        dataset['X'] = data[x_column].values
                    organized['datasets'][y_col] = dataset
            else:
                # Single 2D dataset
                x_column = column_assignments.get('x_column')
                y_columns = column_assignments.get('y_columns', [])

                if x_column and y_columns:
                    dataset = {'X': data[x_column].values}
                    if len(y_columns) == 1:
                        dataset['Y'] = data[y_columns[0]].values
                    else:
                        dataset['Y'] = {col: data[col].values for col in y_columns}
                    organized['datasets'][filename] = dataset

        elif data_type == '3D':
            # 3D data organization
            x_type = column_assignments.get('x_type', 'column')
            y_type = column_assignments.get('y_type', 'column')
            x_index = column_assignments.get('x_index', 0)
            y_index = column_assignments.get('y_index', 0)

            if x_type == 'column' and y_type == 'column':
                # X and Y from columns, Z from remaining data
                x_data = data.iloc[:, x_index].values
                y_data = data.iloc[:, y_index].values

                # Z data from all other columns
                z_cols = [i for i in range(len(data.columns)) if i not in [x_index, y_index]]
                z_data = {data.columns[i]: data.iloc[:, i].values for i in z_cols}

                organized['datasets'][filename] = {
                    'X': x_data,
                    'Y': y_data,
                    'Z': z_data
                }
            elif x_type == 'row' and y_type == 'column':
                # X from row, Y from column
                x_data = data.iloc[x_index, :].values
                y_data = data.iloc[:, y_index].values

                # Z from remaining data
                remaining_data = data.drop(data.index[x_index]).drop(data.columns[y_index], axis=1)
                z_data = {col: remaining_data[col].values for col in remaining_data.columns}

                organized['datasets'][filename] = {
                    'X': x_data,
                    'Y': y_data,
                    'Z': z_data
                }

        return organized

    def save_organized_data(self, organized_data: Dict[str, Any]) -> str:
        """Save organized data to project directory."""

        # Create subdirectory for this import
        import_dir = self.project_dir / f"import_{organized_data['filename']}"
        import_dir.mkdir(exist_ok=True)

        # Save metadata
        metadata = {
            'data_type': organized_data['data_type'],
            'filename': organized_data['filename'],
            'datasets': list(organized_data['datasets'].keys())
        }

        with open(import_dir / 'metadata.json', 'w') as f:
            json.dump(metadata, f, indent=2)

        # Save individual datasets
        for dataset_name, dataset in organized_data['datasets'].items():
            np.savez(import_dir / f'{dataset_name}.npz', **dataset)

        return str(import_dir)


class DataImportGUI:
    """Main GUI for data import module."""

    def __init__(self, master=None, on_complete_callback=None):
        if master is None:
            self.root = tk.Tk()
            self.is_standalone = True
        else:
            self.root = master
            self.is_standalone = False

        self.root.title("Scientific Data Import Module")
        self.root.geometry("1200x800")

        # Callback for when import is complete
        self.on_complete_callback = on_complete_callback

        # Initialize components
        self.detector = DataDetector()
        self.header_detector = HeaderMetadataDetector()
        self.parser = DataParser()
        self.organizer = None

        # Data storage
        self.current_files = []  # List of selected files
        self.current_file = None  # Primary file for parameter detection
        self.detected_params = {}
        self.parsed_data = None
        self.processed_data = None  # Store processed data for plotting
        self.column_assignments = {}
        self.file_analyses = {}  # Dictionary to store analysis for each file
        self.save_headers_choice = False
        self.save_metadata_choice = False
        self.auto_save_file_info = True  # Auto-save file info when headers/metadata detected

        # Store exported files for post-export options
        self.last_exported_files = []
        self.last_export_directory = None

        self.setup_gui()

    def setup_gui(self):
        """Setup the main GUI interface."""

        # Create notebook for tabs
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill='both', expand=True, padx=10, pady=10)

        # Tab 1: File Selection and Detection
        self.tab1 = ttk.Frame(self.notebook)
        self.notebook.add(self.tab1, text="1. File Import")
        self.setup_import_tab()

        # Tab 2: Data Preview and Configuration
        self.tab2 = ttk.Frame(self.notebook)
        self.notebook.add(self.tab2, text="2. Data Configuration")
        self.setup_config_tab()

        # Tab 3: Column Assignment
        self.tab3 = ttk.Frame(self.notebook)
        self.notebook.add(self.tab3, text="3. Column Assignment")
        self.setup_assignment_tab()

        # Add navigation buttons to each tab
        self.add_navigation_buttons()

    def setup_import_tab(self):
        """Setup file import and detection tab."""

        # File selection frame
        file_frame = ttk.LabelFrame(self.tab1, text="File Selection", padding=10)
        file_frame.pack(fill='x', pady=5)

        ttk.Button(file_frame, text="Select File", command=self.select_file).pack(side='left')
        self.file_label = ttk.Label(file_frame, text="No file selected")
        self.file_label.pack(side='left', padx=(10, 0))

        # Detection parameters frame
        detect_frame = ttk.LabelFrame(self.tab1, text="Detected Parameters", padding=10)
        detect_frame.pack(fill='x', pady=5)

        # Delimiter
        ttk.Label(detect_frame, text="Delimiter:").grid(row=0, column=0, sticky='w')
        self.delimiter_var = tk.StringVar()
        delimiter_combo = ttk.Combobox(detect_frame, textvariable=self.delimiter_var,
                                       values=[',', '\\t', ';', '|', ' '], width=10)
        delimiter_combo.grid(row=0, column=1, padx=5, sticky='w')

        # Decimal separator
        ttk.Label(detect_frame, text="Decimal Separator:").grid(row=0, column=2, sticky='w', padx=(20, 0))
        self.decimal_var = tk.StringVar()
        decimal_combo = ttk.Combobox(detect_frame, textvariable=self.decimal_var,
                                     values=['.', ','], width=5)
        decimal_combo.grid(row=0, column=3, padx=5, sticky='w')

        # Header detection options
        header_detection_frame = ttk.LabelFrame(detect_frame, text="Header Detection")
        header_detection_frame.grid(row=1, column=0, columnspan=4, sticky='ew', pady=10, padx=5)

        # Header checkbox
        self.has_header_var = tk.BooleanVar(value=True)
        self.header_checkbox = ttk.Checkbutton(header_detection_frame, text="File has header",
                                               variable=self.has_header_var,
                                               command=self.toggle_header_detection)
        self.header_checkbox.grid(row=0, column=0, sticky='w')

        # Header selection type
        ttk.Label(header_detection_frame, text="Header is:").grid(row=0, column=1, sticky='w', padx=(20, 0))
        self.header_type_var = tk.StringVar(value='line')
        ttk.Radiobutton(header_detection_frame, text="Line", variable=self.header_type_var,
                        value='line', command=self.update_header_options).grid(row=0, column=2, sticky='w')
        ttk.Radiobutton(header_detection_frame, text="Column", variable=self.header_type_var,
                        value='column', command=self.update_header_options).grid(row=0, column=3, sticky='w')

        # Header line/column selection
        self.header_selection_frame = ttk.Frame(header_detection_frame)
        self.header_selection_frame.grid(row=1, column=0, columnspan=4, sticky='ew', pady=5)

        # Data start options
        data_start_frame = ttk.LabelFrame(detect_frame, text="Data Start")
        data_start_frame.grid(row=2, column=0, columnspan=4, sticky='ew', pady=10, padx=5)

        ttk.Label(data_start_frame, text="Data starts at:").grid(row=0, column=0, sticky='w')
        self.data_start_type_var = tk.StringVar(value='line')
        ttk.Radiobutton(data_start_frame, text="Line", variable=self.data_start_type_var,
                        value='line', command=self.update_data_start_options).grid(row=0, column=1, sticky='w')
        ttk.Radiobutton(data_start_frame, text="Column", variable=self.data_start_type_var,
                        value='column', command=self.update_data_start_options).grid(row=0, column=2, sticky='w')

        # Data start line/column selection
        self.data_start_selection_frame = ttk.Frame(data_start_frame)
        self.data_start_selection_frame.grid(row=1, column=0, columnspan=3, sticky='ew', pady=5)

        # Initialize header and data start options
        self.update_header_options()
        self.update_data_start_options()

        # Parse button will be added at the bottom of the tab

        # Header/Metadata detection frame
        header_meta_frame = ttk.LabelFrame(self.tab1, text="Header & Metadata Detection", padding=10)
        header_meta_frame.pack(fill='x', pady=5)

        # Analysis button
        ttk.Button(header_meta_frame, text="Analyze File Content",
                   command=self.analyze_file_content).pack(side='left', padx=5)

        # Results display
        self.analysis_results_label = ttk.Label(header_meta_frame, text="No analysis performed")
        self.analysis_results_label.pack(side='left', padx=(10, 0))

        # Action buttons frame
        self.header_meta_actions_frame = ttk.Frame(header_meta_frame)
        self.header_meta_actions_frame.pack(fill='x', pady=5)

        # Selection checkbox and buttons
        self.save_file_info_var = tk.BooleanVar()

        self.save_file_info_check = ttk.Checkbutton(self.header_meta_actions_frame, text="Include File Info in Export",
                                                   variable=self.save_file_info_var, state='disabled')
        self.dismiss_all_btn = ttk.Button(self.header_meta_actions_frame, text="Dismiss All",
                                         command=self.dismiss_headers_metadata, state='disabled')
        self.show_details_btn = ttk.Button(self.header_meta_actions_frame, text="Show Details",
                                          command=self.show_analysis_details, state='disabled')

        self.save_file_info_check.pack(side='left', padx=5)
        self.dismiss_all_btn.pack(side='left', padx=5)
        self.show_details_btn.pack(side='left', padx=5)

        # Preview frame
        preview_frame = ttk.LabelFrame(self.tab1, text="File Preview", padding=10)
        preview_frame.pack(fill='both', expand=True, pady=5)

        # Text widget for preview
        self.preview_text = tk.Text(preview_frame, height=15, font=('Courier', 9))
        preview_scrollbar = ttk.Scrollbar(preview_frame, orient='vertical', command=self.preview_text.yview)
        self.preview_text.configure(yscrollcommand=preview_scrollbar.set)

        self.preview_text.pack(side='left', fill='both', expand=True)
        preview_scrollbar.pack(side='right', fill='y')

    def update_header_options(self):
        """Update header selection options based on type."""
        # Clear existing widgets
        for widget in self.header_selection_frame.winfo_children():
            widget.destroy()

        if not self.has_header_var.get():
            return

        header_type = self.header_type_var.get()

        if header_type == 'line':
            ttk.Label(self.header_selection_frame, text="Header line:").pack(side='left')
            self.header_var = tk.StringVar(value='0') 
            header_spin = ttk.Spinbox(self.header_selection_frame, textvariable=self.header_var,
                                      from_=0, to=51, width=10)
            header_spin.pack(side='left', padx=5)
        else:  # column
            ttk.Label(self.header_selection_frame, text="Header column:").pack(side='left')
            self.header_var = tk.StringVar(value='0')
            header_spin = ttk.Spinbox(self.header_selection_frame, textvariable=self.header_var,
                                      from_=0, to=51, width=10)
            header_spin.pack(side='left', padx=5)

    def update_data_start_options(self):
        """Update data start selection options based on type."""
        # Clear existing widgets
        for widget in self.data_start_selection_frame.winfo_children():
            widget.destroy()

        data_start_type = self.data_start_type_var.get()

        if data_start_type == 'line':
            ttk.Label(self.data_start_selection_frame, text="Data start line:").pack(side='left')
            self.data_start_var = tk.StringVar(value='1')  
            data_spin = ttk.Spinbox(self.data_start_selection_frame, textvariable=self.data_start_var,
                                    from_=0, to=51, width=10)
            data_spin.pack(side='left', padx=5)
        else:  # column
            ttk.Label(self.data_start_selection_frame, text="Data start column:").pack(side='left')
            self.data_start_var = tk.StringVar(value='1')
            data_spin = ttk.Spinbox(self.data_start_selection_frame, textvariable=self.data_start_var,
                                    from_=0, to=51, width=10)
            data_spin.pack(side='left', padx=5)

    def setup_config_tab(self):
        """Setup data configuration tab."""

        # Data exclusion frame
        exclude_frame = ttk.LabelFrame(self.tab2, text="Data Exclusion (Use displayed row/column indices)", padding=10)
        exclude_frame.pack(fill='x', pady=5)

        ttk.Label(exclude_frame, text="Exclude Rows (comma-separated):").pack(anchor='w')
        self.exclude_rows_var = tk.StringVar()
        exclude_rows_entry = ttk.Entry(exclude_frame, textvariable=self.exclude_rows_var, width=50)
        exclude_rows_entry.pack(fill='x', pady=2)
        exclude_rows_entry.bind('<KeyRelease>', self.on_exclusion_change)

        ttk.Label(exclude_frame, text="Exclude Columns (comma-separated):").pack(anchor='w', pady=(10, 0))
        self.exclude_cols_var = tk.StringVar()
        exclude_cols_entry = ttk.Entry(exclude_frame, textvariable=self.exclude_cols_var, width=50)
        exclude_cols_entry.pack(fill='x', pady=2)
        exclude_cols_entry.bind('<KeyRelease>', self.on_exclusion_change)

        # Data preview frame
        data_frame = ttk.LabelFrame(self.tab2, text="Parsed Data Preview", padding=10)
        data_frame.pack(fill='both', expand=True, pady=5)

        # Create frame for treeview and scrollbars
        tree_frame = ttk.Frame(data_frame)
        tree_frame.pack(fill='both', expand=True)

        # Treeview for data preview
        self.data_tree = ttk.Treeview(tree_frame)

        # Scrollbars
        data_tree_scroll_y = ttk.Scrollbar(tree_frame, orient='vertical', command=self.data_tree.yview)
        data_tree_scroll_x = ttk.Scrollbar(tree_frame, orient='horizontal', command=self.data_tree.xview)
        self.data_tree.configure(yscrollcommand=data_tree_scroll_y.set, xscrollcommand=data_tree_scroll_x.set)

        # Pack treeview and scrollbars
        self.data_tree.grid(row=0, column=0, sticky='nsew')
        data_tree_scroll_y.grid(row=0, column=1, sticky='ns')
        data_tree_scroll_x.grid(row=1, column=0, sticky='ew')

        # Configure grid weights
        tree_frame.grid_rowconfigure(0, weight=1)
        tree_frame.grid_columnconfigure(0, weight=1)

        # Info label
        self.data_info_label = ttk.Label(data_frame, text="No data loaded")
        self.data_info_label.pack(pady=5)
        
        
        # Refresh button
        ttk.Button(data_frame, text="Apply Exclusions", command=self.apply_exclusions).pack(side='bottom', pady=5)

    def setup_assignment_tab(self):
        """Setup column assignment tab with data preview."""
        # Create main container with paned window
        main_paned = ttk.PanedWindow(self.tab3, orient='horizontal')
        main_paned.pack(fill='both', expand=True, padx=10, pady=10)

        # Left frame for assignment controls
        left_frame = ttk.Frame(main_paned)
        main_paned.add(left_frame, weight=1)

        # Right frame for data preview
        right_frame = ttk.Frame(main_paned)
        main_paned.add(right_frame, weight=1)

        # Setup left side (assignment controls)
        # Canvas and scrollbar for scrollable content
        canvas = tk.Canvas(left_frame)
        scrollbar = ttk.Scrollbar(left_frame, orient="vertical", command=canvas.yview)
        self.scrollable_frame = ttk.Frame(canvas)

        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Data type selection
        type_frame = ttk.LabelFrame(self.scrollable_frame, text="Data Type", padding=10)
        type_frame.pack(fill='x', pady=5)

        self.data_type_var = tk.StringVar(value='2D')
        ttk.Radiobutton(type_frame, text="1D Data", variable=self.data_type_var,
                        value='1D', command=self.update_assignment_options).pack(side='left')
        ttk.Radiobutton(type_frame, text="2D Data", variable=self.data_type_var,
                        value='2D', command=self.update_assignment_options).pack(side='left', padx=20)
        ttk.Radiobutton(type_frame, text="3D Data", variable=self.data_type_var,
                        value='3D', command=self.update_assignment_options).pack(side='left')

        # Assignment options frame
        self.assignment_frame = ttk.LabelFrame(self.scrollable_frame, text="Assignment Options", padding=10)
        self.assignment_frame.pack(fill='both', expand=True, pady=5)

        self.assignment_widgets = {}

        # Setup right side (data preview)
        preview_frame = ttk.LabelFrame(right_frame, text="Data Preview", padding=10)
        preview_frame.pack(fill='both', expand=True)

        # Create frame for treeview and scrollbars
        tree_frame = ttk.Frame(preview_frame)
        tree_frame.pack(fill='both', expand=True)

        # Treeview for assignment preview
        self.assignment_preview_tree = ttk.Treeview(tree_frame)

        # Scrollbars
        preview_scroll_y = ttk.Scrollbar(tree_frame, orient='vertical', command=self.assignment_preview_tree.yview)
        preview_scroll_x = ttk.Scrollbar(tree_frame, orient='horizontal', command=self.assignment_preview_tree.xview)
        self.assignment_preview_tree.configure(yscrollcommand=preview_scroll_y.set, xscrollcommand=preview_scroll_x.set)

        # Pack treeview and scrollbars
        self.assignment_preview_tree.grid(row=0, column=0, sticky='nsew')
        preview_scroll_y.grid(row=0, column=1, sticky='ns')
        preview_scroll_x.grid(row=1, column=0, sticky='ew')

        # Configure grid weights
        tree_frame.grid_rowconfigure(0, weight=1)
        tree_frame.grid_columnconfigure(0, weight=1)

        # Preview info label
        self.assignment_preview_label = ttk.Label(preview_frame,
                                                  text="Select assignment options to preview data organization")
        self.assignment_preview_label.pack(pady=5)

        # Refresh preview button
        ttk.Button(preview_frame, text="Refresh Preview", command=self.refresh_assignment_preview).pack(pady=5)

    def refresh_assignment_preview(self):
        """Refresh the assignment preview based on current selections."""
        if self.parsed_data is None or self.parsed_data.empty:
            self.assignment_preview_label.config(text="No data available for preview")
            return

        try:
            # Collect current assignments
            assignments = self.collect_column_assignments()

            # Clear existing preview
            for item in self.assignment_preview_tree.get_children():
                self.assignment_preview_tree.delete(item)

            # Generate preview based on assignment type
            data_type = assignments.get('data_type', '2D')

            if data_type == '1D':
                self.preview_1d_assignment(assignments)
            elif data_type == '2D':
                self.preview_2d_assignment(assignments)
            elif data_type == '3D':
                self.preview_3d_assignment(assignments)

        except Exception as e:
            self.assignment_preview_label.config(text=f"Preview error: {str(e)}")



    def toggle_header_detection(self):
        """Toggle header detection based on checkbox."""
        if self.has_header_var.get():
            self.update_header_options()
        else:
            # Clear header options when disabled
            for widget in self.header_selection_frame.winfo_children():
                widget.destroy()
            self.header_var = tk.StringVar(value='-1')

    def select_file(self):
        """Select file(s) for import."""
        file_paths = filedialog.askopenfilenames(
            title="Select Data File(s) - Choose multiple files with same structure",
            filetypes=[("All supported", "*.csv;*.txt;*.dat;*.tsv;*.scan;*.xml"),
                       ("CSV files", "*.csv"),
                       ("Text files", "*.txt"),
                       ("Data files", "*.dat"),
                       ("TSV files", "*.tsv"),
                       ("Scan files", "*.scan"),
                       ("XML files", "*.xml"),
                       ("All files", "*.*")]
        )

        if file_paths:
            self.current_files = list(file_paths)
            self.current_file = file_paths[0]  # Use first file as primary for parameter detection

            # Update label to show file count
            if len(file_paths) == 1:
                self.file_label.config(text=Path(file_paths[0]).name)
            else:
                self.file_label.config(text=f"{len(file_paths)} files selected (Primary: {Path(file_paths[0]).name})")

            # Clear parse status when new file is selected
            self.parse_status_label.config(text="")

            # Auto-detect parameters using primary file
            delimiter = self.detector.detect_delimiter(self.current_file)
            decimal_sep = self.detector.detect_decimal_separator(self.current_file, delimiter)
            header_line, data_start = self.detector.detect_header_and_data_start(self.current_file, delimiter, decimal_sep)

            # Update GUI with detected values
            self.delimiter_var.set('\\t' if delimiter == '\t' else delimiter)
            self.decimal_var.set(decimal_sep)

            # Set header detection based on whether header was found
            if header_line >= 0:
                self.has_header_var.set(True)
                self.header_var.set(str(header_line))
            else:
                self.has_header_var.set(False)
                self.header_var.set('-1')

            self.data_start_var.set(str(data_start))

            # Show file preview of primary file
            self.show_file_preview()

            # Reset analysis results
            self.file_analyses = {}
            self.analysis_results_label.config(text="No analysis performed")
            self._disable_analysis_buttons()

            # Show multi-file info
            if len(file_paths) > 1:
                messagebox.showinfo("Multi-File Import",
                                   f"Selected {len(file_paths)} files for batch processing.\n\n"
                                   f"Primary file: {Path(file_paths[0]).name}\n"
                                   f"- Parameter detection will use the primary file\n"
                                   f"- Same parsing settings will apply to all files\n"
                                   f"- Each file's metadata will be analyzed individually\n\n"
                                   f"Files: {', '.join([Path(f).name for f in file_paths[:5]])}"
                                   f"{'...' if len(file_paths) > 5 else ''}")

    def show_file_preview(self):
        """Show raw file preview."""
        if not self.current_file:
            return

        self.preview_text.delete(1.0, tk.END)

        try:
            with open(self.current_file, 'r', encoding='utf-8', errors='ignore') as f:
                lines = f.readlines()[:50]  # Show first 50 lines

            for i, line in enumerate(lines):
                # Display 1-based line numbers for user-friendly interface
                self.preview_text.insert(tk.END, f"{i:2d}: {line}")

        except Exception as e:
            self.preview_text.insert(tk.END, f"Error reading file: {str(e)}")

    def analyze_file_content(self):
        """Analyze file(s) for headers and metadata."""
        if not self.current_files:
            messagebox.showerror("Error", "Please select file(s) first")
            return

        try:
            # Get current delimiter setting
            delimiter = self.delimiter_var.get().replace('\\t', '\t')

            # Analyze all selected files
            self.file_analyses = {}
            total_headers = 0
            total_metadata = 0

            progress_msg = f"Analyzing {len(self.current_files)} file(s)..."
            self.analysis_results_label.config(text=progress_msg)
            self.root.update()  # Update GUI

            for file_path in self.current_files:
                analysis = self.header_detector.analyze_file_content(file_path, delimiter)
                self.file_analyses[file_path] = analysis
                total_headers += len(analysis['headers'])
                total_metadata += len(analysis['metadata'])

            # Update results display
            if len(self.current_files) == 1:
                result_text = f"Found: {total_headers} headers, {total_metadata} metadata lines"
            else:
                result_text = f"Found across {len(self.current_files)} files: {total_headers} headers, {total_metadata} metadata lines"

            self.analysis_results_label.config(text=result_text)

            # Enable action buttons if content was found
            if total_headers > 0 or total_metadata > 0:
                self._enable_analysis_buttons()

                # Show summary in a message box
                if len(self.current_files) == 1:
                    # Single file summary
                    analysis = list(self.file_analyses.values())[0]
                    summary = f"Analysis Results:\n\n"
                    summary += f"File: {Path(self.current_file).name}\n"
                    summary += f"Total lines: {analysis['total_lines']}\n"
                    summary += f"Data starts at line: {analysis['data_start']}\n"
                    summary += f"Data ends at line: {analysis['data_end']}\n\n"
                    summary += f"Headers found: {len(analysis['headers'])}\n"
                    summary += f"Metadata found: {len(analysis['metadata'])}\n"
                else:
                    # Multi-file summary
                    summary = f"Multi-File Analysis Results:\n\n"
                    summary += f"Files analyzed: {len(self.current_files)}\n"
                    summary += f"Total headers found: {total_headers}\n"
                    summary += f"Total metadata found: {total_metadata}\n\n"

                    summary += "Per-file breakdown:\n"
                    for i, (file_path, analysis) in enumerate(self.file_analyses.items()):
                        if i >= 5:  # Show first 5 files
                            summary += f"  ... and {len(self.file_analyses) - 5} more files\n"
                            break
                        summary += f"  {Path(file_path).name}: {len(analysis['headers'])} headers, {len(analysis['metadata'])} metadata\n"

                summary += "\nWhat would you like to do with this content?"
                messagebox.showinfo("Analysis Complete", summary)
            else:
                self._disable_analysis_buttons()
                if len(self.current_files) == 1:
                    messagebox.showinfo("Analysis Complete", "No headers or metadata detected in the file.")
                else:
                    messagebox.showinfo("Analysis Complete", f"No headers or metadata detected in any of the {len(self.current_files)} files.")

        except Exception as e:
            messagebox.showerror("Error", f"Error analyzing file(s): {str(e)}")

    def _enable_analysis_buttons(self):
        """Enable analysis action buttons."""
        if self.file_analyses:
            # Check if any file has headers or metadata
            has_content = any(
                analysis['headers'] or analysis['metadata']
                for analysis in self.file_analyses.values()
            )
            if has_content:
                self.save_file_info_check.config(state='normal')
            self.dismiss_all_btn.config(state='normal')
            self.show_details_btn.config(state='normal')

    def _disable_analysis_buttons(self):
        """Disable analysis action buttons."""
        self.save_file_info_check.config(state='disabled')
        self.dismiss_all_btn.config(state='disabled')
        self.show_details_btn.config(state='disabled')
        self.save_file_info_var.set(False)



    def dismiss_headers_metadata(self):
        """Dismiss detected headers and metadata."""
        if not self.file_analyses:
            return

        file_count = len(self.file_analyses)
        result = messagebox.askyesno("Confirm Dismiss",
                                   f"Are you sure you want to dismiss all detected headers and metadata from {file_count} file(s)?\n\n"
                                   "This will clear the analysis and uncheck all save options.")

        if result:
            self.file_analyses = {}
            self.analysis_results_label.config(text="Headers and metadata dismissed")
            self._disable_analysis_buttons()
            # Re-parse the primary file without exclusions
            self.parse_file()

    def show_analysis_details(self):
        """Show detailed analysis results in a new window."""
        if not self.file_analyses:
            return

        # Create details window
        details_window = tk.Toplevel(self.root)
        details_window.title("Header & Metadata Analysis Details")
        details_window.geometry("900x700")

        # Create notebook for tabs
        notebook = ttk.Notebook(details_window)
        notebook.pack(fill='both', expand=True, padx=10, pady=10)

        # If multiple files, create a tab for each file
        if len(self.file_analyses) > 1:
            for file_path, analysis in self.file_analyses.items():
                file_name = Path(file_path).name
                self._create_analysis_tab(notebook, analysis, file_name)
        else:
            # Single file - use the original layout
            analysis = list(self.file_analyses.values())[0]
            self._create_analysis_tab(notebook, analysis, "Analysis")

    def _create_analysis_tab(self, notebook, analysis, tab_name):
        """Create a tab with analysis details for a single file."""
        main_frame = ttk.Frame(notebook)
        notebook.add(main_frame, text=tab_name)

        # Create sub-notebook for headers/metadata/summary
        sub_notebook = ttk.Notebook(main_frame)
        sub_notebook.pack(fill='both', expand=True)

        # Headers tab
        if analysis['headers']:
            headers_frame = ttk.Frame(sub_notebook)
            sub_notebook.add(headers_frame, text=f"Headers ({len(analysis['headers'])})")

            headers_tree = ttk.Treeview(headers_frame, columns=['Line', 'Content', 'Confidence', 'Reason'], show='headings')
            headers_tree.heading('Line', text='Line #')
            headers_tree.heading('Content', text='Content')
            headers_tree.heading('Confidence', text='Confidence')
            headers_tree.heading('Reason', text='Reason')

            for header in analysis['headers']:
                headers_tree.insert('', 'end', values=[
                    header['line_number'],
                    header['content'][:100] + '...' if len(header['content']) > 100 else header['content'],
                    f"{header['confidence']:.2f}",
                    header['reason']
                ])

            headers_tree.pack(fill='both', expand=True)

        # Metadata tab
        if analysis['metadata']:
            metadata_frame = ttk.Frame(sub_notebook)
            sub_notebook.add(metadata_frame, text=f"Metadata ({len(analysis['metadata'])})")

            metadata_tree = ttk.Treeview(metadata_frame, columns=['Line', 'Content', 'Confidence', 'Reason'], show='headings')
            metadata_tree.heading('Line', text='Line #')
            metadata_tree.heading('Content', text='Content')
            metadata_tree.heading('Confidence', text='Confidence')
            metadata_tree.heading('Reason', text='Reason')

            for metadata in analysis['metadata']:
                metadata_tree.insert('', 'end', values=[
                    metadata['line_number'],
                    metadata['content'][:100] + '...' if len(metadata['content']) > 100 else metadata['content'],
                    f"{metadata['confidence']:.2f}",
                    metadata['reason']
                ])

            metadata_tree.pack(fill='both', expand=True)

        # Summary tab
        summary_frame = ttk.Frame(sub_notebook)
        sub_notebook.add(summary_frame, text="Summary")

        summary_text = tk.Text(summary_frame, wrap='word')
        summary_scroll = ttk.Scrollbar(summary_frame, orient='vertical', command=summary_text.yview)
        summary_text.configure(yscrollcommand=summary_scroll.set)

        summary_content = f"""File Analysis Summary
{'=' * 50}

File: {tab_name}
Total lines: {analysis['total_lines']}
Delimiter: '{analysis['delimiter']}'

Data boundaries:
  Start: Line {analysis['data_start']}
  End: Line {analysis['data_end']}

Headers detected: {len(analysis['headers'])}
Metadata detected: {len(analysis['metadata'])}

Recommendations:
"""

        if analysis['headers']:
            summary_content += f"- Consider saving {len(analysis['headers'])} header lines to preserve column information\n"

        if analysis['metadata']:
            summary_content += f"- Consider saving {len(analysis['metadata'])} metadata lines to preserve experimental conditions\n"

        if not analysis['headers'] and not analysis['metadata']:
            summary_content += "- No headers or metadata detected - file appears to contain only data\n"

        summary_text.insert('1.0', summary_content)
        summary_text.config(state='disabled')

        summary_text.pack(side='left', fill='both', expand=True)
        summary_scroll.pack(side='right', fill='y')

    def add_navigation_buttons(self):
        """Add Back/Proceed navigation buttons to each tab."""

        # Tab 1: File Import - Parse File button and Proceed button (enabled after parsing)
        nav_frame1 = ttk.Frame(self.tab1)
        nav_frame1.pack(side='bottom', fill='x', pady=10)

        # Parse File button (always enabled if file is selected)
        parse_frame = ttk.Frame(nav_frame1)
        parse_frame.pack(side='left', padx=5)

        self.parse_btn = ttk.Button(parse_frame, text="Parse File", command=self.parse_file)
        self.parse_btn.pack(side='top', padx=5)

        # Status indicator next to parse button
        self.parse_status_label = ttk.Label(parse_frame, text="", font=('TkDefaultFont', 12))
        self.parse_status_label.pack(side='top', padx=(5, 0))

        self.proceed_btn1 = ttk.Button(nav_frame1, text="Proceed",
                                      command=lambda: self.navigate_to_tab(1), state='disabled')
        self.proceed_btn1.pack(side='right', padx=5)

        # Tab 2: Data Configuration - Back and Proceed buttons
        nav_frame2 = ttk.Frame(self.tab2)
        nav_frame2.pack(side='bottom', fill='x', pady=10)

        ttk.Button(nav_frame2, text="Back",
                  command=lambda: self.navigate_to_tab(0)).pack(side='left', padx=5)
        ttk.Button(nav_frame2, text="Proceed",
                  command=lambda: self.navigate_to_tab(2)).pack(side='right', padx=5)

        # Tab 3: Column Assignment - Back and Proceed buttons
        nav_frame3 = ttk.Frame(self.tab3)
        nav_frame3.pack(side='bottom', fill='x', pady=10)

        ttk.Button(nav_frame3, text="Back",
                  command=lambda: self.navigate_to_tab(1)).pack(side='left', padx=5)
        ttk.Button(nav_frame3, text="Proceed",
                  command=self.proceed_to_plotting).pack(side='right', padx=5)

    def navigate_to_tab(self, tab_index):
        """Navigate to a specific tab."""
        self.notebook.select(tab_index)

    def toggle_folder_entry(self):
        """Toggle the folder name entry field based on checkbox state."""
        if self.create_folder_var.get():
            self.folder_entry.config(state='normal')
        else:
            self.folder_entry.config(state='disabled')
            self.folder_name_var.set('')

    def add_column_naming_section(self, data_type):
        """Add column naming section for any data type."""
        naming_frame = ttk.LabelFrame(self.assignment_frame, text="Column Names", padding=10)
        naming_frame.pack(fill='x', pady=5)

        ttk.Label(naming_frame, text="Customize column names for export:").grid(row=0, column=0, columnspan=3, sticky='w')

        # X column name (for all data types)
        ttk.Label(naming_frame, text="X column name:").grid(row=1, column=0, sticky='w', padx=(0, 5))
        if 'x_name' not in self.assignment_widgets:
            self.assignment_widgets['x_name'] = tk.StringVar(value='X')
        ttk.Entry(naming_frame, textvariable=self.assignment_widgets['x_name'], width=15).grid(row=1, column=1, sticky='w', padx=5)

        # Y column name (for 2D and 3D)
        if data_type in ['2D', '3D']:
            ttk.Label(naming_frame, text="Y column name:").grid(row=2, column=0, sticky='w', padx=(0, 5))
            if 'y_name' not in self.assignment_widgets:
                self.assignment_widgets['y_name'] = tk.StringVar(value='Y')
            ttk.Entry(naming_frame, textvariable=self.assignment_widgets['y_name'], width=15).grid(row=2, column=1, sticky='w', padx=5)

        # Z column name (for 3D only)
        if data_type == '3D':
            ttk.Label(naming_frame, text="Z column name:").grid(row=3, column=0, sticky='w', padx=(0, 5))
            if 'z_name' not in self.assignment_widgets:
                self.assignment_widgets['z_name'] = tk.StringVar(value='Z')
            ttk.Entry(naming_frame, textvariable=self.assignment_widgets['z_name'], width=15).grid(row=3, column=1, sticky='w', padx=5)

        # Auto-fill from headers button
        auto_fill_btn = ttk.Button(naming_frame, text="Auto-fill from Headers",
                                  command=self.auto_fill_column_names)
        if data_type == '1D':
            auto_fill_btn.grid(row=1, column=2, padx=10, sticky='w')
        elif data_type == '2D':
            auto_fill_btn.grid(row=1, column=2, rowspan=2, padx=10, sticky='ns')
        else:  # 3D
            auto_fill_btn.grid(row=1, column=2, rowspan=3, padx=10, sticky='ns')

    def auto_fill_column_names(self):
        """Auto-fill column names from detected headers."""
        if not self.file_analyses:
            messagebox.showinfo("No Headers", "No file analysis available. Please analyze file content first.")
            return

        # Use primary file analysis
        primary_analysis = self.file_analyses.get(self.current_file)
        if not primary_analysis or not primary_analysis.get('headers'):
            messagebox.showinfo("No Headers", "No column headers were detected in the primary file.")
            return

        # Get the header line
        header_line = primary_analysis['headers'][0]['content']
        delimiter = primary_analysis.get('delimiter', ',')

        # Split header into column names
        column_names = [name.strip() for name in header_line.split(delimiter) if name.strip()]

        if len(column_names) >= 1:
            self.assignment_widgets['x_name'].set(column_names[0])
        if len(column_names) >= 2:
            self.assignment_widgets['y_name'].set(column_names[1])
        if len(column_names) >= 3:
            self.assignment_widgets['z_name'].set(column_names[2])

        messagebox.showinfo("Auto-fill Complete",
                           f"Column names filled from header:\nX: {column_names[0] if len(column_names) >= 1 else 'N/A'}\n"
                           f"Y: {column_names[1] if len(column_names) >= 2 else 'N/A'}\n"
                           f"Z: {column_names[2] if len(column_names) >= 3 else 'N/A'}")

    def parse_file(self):
        """Parse file with current parameters."""
        if not self.current_file:
            messagebox.showerror("Error", "Please select a file first")
            return

        try:
            delimiter = self.delimiter_var.get().replace('\\t', '\t')
            decimal_sep = self.decimal_var.get()

            # Handle header detection (convert from 1-based user input to 0-based internal)
            if self.has_header_var.get():
                header_line = int(self.header_var.get()) if self.header_var.get() != '-1' else -1
            else:
                header_line = -1

            data_start = int(self.data_start_var.get())  # Convert from 1-based to 0-based

            # Parse the primary file (without exclusions initially)
            # Use analysis from primary file if available
            primary_analysis = self.file_analyses.get(self.current_file) if self.file_analyses else None
            self.parsed_data = self.parser.parse_file(
                self.current_file, delimiter, decimal_sep, header_line, data_start,
                file_analysis=primary_analysis
            )

            # Update data preview
            self.refresh_data_preview()

            # Update column assignment options
            self.update_assignment_options()

            # Show green checkmark instead of popup
            self.parse_status_label.config(text="✓", foreground="green")

            # Enable the Proceed button after successful parsing
            self.proceed_btn1.config(state='normal')

        except Exception as e:
            # Show red X for error
            self.parse_status_label.config(text="✗", foreground="red")
            messagebox.showerror("Error", f"Error parsing file: {str(e)}")
            # Disable the Proceed button if parsing fails
            self.proceed_btn1.config(state='disabled')

    def on_exclusion_change(self, event=None):
        """Handle changes to exclusion entries."""
        # This method can be used to provide real-time feedback
        pass

    def on_assignment_change(self, event=None):
        """Handle changes to column assignments."""
        # Update export summary and prepare processing
        self.update_export_summary()

    def refresh_data_preview(self):
        """Refresh the data preview in the treeview."""
        if self.parsed_data is None or self.parsed_data.empty:
            self.data_info_label.config(text="No data loaded")
            return

        # Clear existing items
        for item in self.data_tree.get_children():
            self.data_tree.delete(item)

        # Configure columns
        columns = ['Index'] + list(self.parsed_data.columns)
        self.data_tree['columns'] = columns
        self.data_tree['show'] = 'headings'

        # Configure column headings and widths
        for col in columns:
            self.data_tree.heading(col, text=col)
            self.data_tree.column(col, width=100, minwidth=50)

        # Insert data rows (showing DISPLAYED indices)
        for idx, (_, row) in enumerate(self.parsed_data.iterrows()):
            values = [str(idx)] + [str(val) for val in row.values]
            self.data_tree.insert('', 'end', values=values)

        # Update info label
        shape = self.parsed_data.shape
        self.data_info_label.config(text=f"Data shape: {shape[0]} rows × {shape[1]} columns")

    def apply_exclusions(self):
        """Apply exclusions based on DISPLAYED indices and refresh preview."""
        if self.parser.original_data is None:
            messagebox.showerror("Error", "No original data available")
            return

        try:
            # Parse excluded rows and columns from user input
            excluded_rows = []
            if self.exclude_rows_var.get().strip():
                excluded_rows = [int(x.strip()) for x in self.exclude_rows_var.get().split(',') if x.strip()]

            excluded_cols = []
            if self.exclude_cols_var.get().strip():
                excluded_cols = [int(x.strip()) for x in self.exclude_cols_var.get().split(',') if x.strip()]

            # Start with original data
            df = self.parser.original_data.copy()

            # Apply exclusions based on DISPLAYED indices
            if excluded_rows:
                # Remove rows by displayed index
                valid_row_indices = [i for i in excluded_rows if 0 <= i < len(df)]
                if valid_row_indices:
                    df = df.drop(df.index[valid_row_indices])

            if excluded_cols:
                # Remove columns by displayed index
                cols_to_drop = [df.columns[i] for i in excluded_cols if 0 <= i < len(df.columns)]
                if cols_to_drop:
                    df = df.drop(columns=cols_to_drop)

            # Update parsed data
            self.parsed_data = df
            self.parser.raw_data = df
            self.parser.data_shape = df.shape

            # Refresh preview
            self.refresh_data_preview()

            # Update assignment options
            self.update_assignment_options()

            messagebox.showinfo("Success", "Exclusions applied successfully!")

        except ValueError as e:
            messagebox.showerror("Error", f"Invalid input: {str(e)}")
        except Exception as e:
            messagebox.showerror("Error", f"Error applying exclusions: {str(e)}")

    def update_assignment_options(self):
        """Update column assignment options based on data type."""
        # Clear existing widgets
        for widget in self.assignment_frame.winfo_children():
            widget.destroy()

        self.assignment_widgets.clear()

        if self.parsed_data is None or self.parsed_data.empty:
            ttk.Label(self.assignment_frame, text="No data available for assignment").pack()
            return

        data_type = self.data_type_var.get()
        columns = list(self.parsed_data.columns)
        n_rows, n_cols = self.parsed_data.shape

        if data_type == '1D':
            self.setup_1d_assignment(columns, n_rows, n_cols)
        elif data_type == '2D':
            self.setup_2d_assignment(columns, n_rows, n_cols)
        elif data_type == '3D':
            self.setup_3d_assignment(columns, n_rows, n_cols)

        # Trigger assignment change to update processing
        self.on_assignment_change()

    def setup_1d_assignment(self, columns, n_rows, n_cols):
        """Setup 1D data assignment options."""
        # Subtype selection
        subtype_frame = ttk.LabelFrame(self.assignment_frame, text="1D Data Type", padding=10)
        subtype_frame.pack(fill='x', pady=5)

        self.assignment_widgets['subtype'] = tk.StringVar(value='list')
        ttk.Radiobutton(subtype_frame, text="Single List (one column/row)",
                        variable=self.assignment_widgets['subtype'], value='list',
                        command=self.update_1d_options).pack(anchor='w')
        ttk.Radiobutton(subtype_frame, text="Table (multiple columns/rows as datasets)",
                        variable=self.assignment_widgets['subtype'], value='table',
                        command=self.update_1d_options).pack(anchor='w')

        # Options frame
        self.assignment_widgets['options_frame'] = ttk.LabelFrame(self.assignment_frame,
                                                                  text="Assignment Options", padding=10)
        self.assignment_widgets['options_frame'].pack(fill='x', pady=5)

        self.update_1d_options()

        # Column naming section for 1D
        self.add_column_naming_section("1D")

    def update_1d_options(self):
        """Update 1D assignment options based on subtype."""
        options_frame = self.assignment_widgets['options_frame']

        # Clear existing options
        for widget in options_frame.winfo_children():
            widget.destroy()

        subtype = self.assignment_widgets['subtype'].get()
        columns = list(self.parsed_data.columns)
        n_rows = len(self.parsed_data)

        if subtype == 'list':
            # Single column or row selection
            ttk.Label(options_frame, text="Source Type:").grid(row=0, column=0, sticky='w', pady=2)

            self.assignment_widgets['source_type'] = tk.StringVar(value='column')
            ttk.Radiobutton(options_frame, text="Column",
                            variable=self.assignment_widgets['source_type'], value='column',
                            command=self.update_source_options).grid(row=0, column=1, sticky='w')
            ttk.Radiobutton(options_frame, text="Row",
                            variable=self.assignment_widgets['source_type'], value='row',
                            command=self.update_source_options).grid(row=0, column=2, sticky='w')

            # Source index frame
            self.assignment_widgets['source_frame'] = ttk.Frame(options_frame)
            self.assignment_widgets['source_frame'].grid(row=1, column=0, columnspan=3, sticky='ew', pady=5)

            self.update_source_options()

        elif subtype == 'table':
            # Table handling with inclusion/exclusion options
            ttk.Label(options_frame, text="Table Processing:").grid(row=0, column=0, sticky='nw', pady=2)

            # Selection method
            self.assignment_widgets['selection_method'] = tk.StringVar(value='include')
            method_frame = ttk.Frame(options_frame)
            method_frame.grid(row=0, column=1, sticky='w', pady=2)

            ttk.Radiobutton(method_frame, text="Include selected",
                            variable=self.assignment_widgets['selection_method'],
                            value='include', command=self.update_table_selection).pack(anchor='w')
            ttk.Radiobutton(method_frame, text="Exclude selected",
                            variable=self.assignment_widgets['selection_method'],
                            value='exclude', command=self.update_table_selection).pack(anchor='w')

            # Column/Row selection
            self.assignment_widgets['table_selection_frame'] = ttk.Frame(options_frame)
            self.assignment_widgets['table_selection_frame'].grid(row=1, column=0, columnspan=2, sticky='ew', pady=5)

            self.update_table_selection()

            # Optional X column for table mode
            ttk.Label(options_frame, text="X data (optional):").grid(row=2, column=0, sticky='w', pady=(10, 2))
            x_frame = ttk.Frame(options_frame)
            x_frame.grid(row=2, column=1, sticky='w', pady=(10, 2))

            self.assignment_widgets['x_source'] = tk.StringVar(value='none')
            ttk.Radiobutton(x_frame, text="None", variable=self.assignment_widgets['x_source'],
                            value='none', command=self.update_x_selection).pack(side='left')
            ttk.Radiobutton(x_frame, text="Column", variable=self.assignment_widgets['x_source'],
                            value='column', command=self.update_x_selection).pack(side='left')
            ttk.Radiobutton(x_frame, text="Row", variable=self.assignment_widgets['x_source'],
                            value='row', command=self.update_x_selection).pack(side='left')

            # X selection frame
            self.assignment_widgets['x_selection_frame'] = ttk.Frame(options_frame)
            self.assignment_widgets['x_selection_frame'].grid(row=3, column=0, columnspan=2, sticky='ew', pady=5)

            self.update_x_selection()

    def update_table_selection(self):
        """Update table selection options for 1D table mode."""
        selection_frame = self.assignment_widgets['table_selection_frame']

        # Clear existing widgets
        for widget in selection_frame.winfo_children():
            widget.destroy()

        columns = list(self.parsed_data.columns)
        n_rows = len(self.parsed_data)
        selection_method = self.assignment_widgets['selection_method'].get()

        # Create notebook for columns and rows
        selection_notebook = ttk.Notebook(selection_frame)
        selection_notebook.pack(fill='both', expand=True)

        # Columns tab
        cols_frame = ttk.Frame(selection_notebook)
        selection_notebook.add(cols_frame, text="Columns")

        action_text = "Include" if selection_method == 'include' else "Exclude"
        ttk.Label(cols_frame, text=f"{action_text} columns:").pack(anchor='w')

        # Create scrollable frame for columns
        cols_canvas = tk.Canvas(cols_frame, height=150)
        cols_scrollbar = ttk.Scrollbar(cols_frame, orient="vertical", command=cols_canvas.yview)
        cols_scrollable = ttk.Frame(cols_canvas)

        cols_scrollable.bind("<Configure>", lambda e: cols_canvas.configure(scrollregion=cols_canvas.bbox("all")))
        cols_canvas.create_window((0, 0), window=cols_scrollable, anchor="nw")
        cols_canvas.configure(yscrollcommand=cols_scrollbar.set)

        self.assignment_widgets['column_selection'] = {}
        for i, col in enumerate(columns):
            var = tk.BooleanVar()
            if selection_method == 'include':
                var.set(True)  # Default include all
            ttk.Checkbutton(cols_scrollable, text=f"{col} (Col {i})", variable=var).pack(anchor='w')
            self.assignment_widgets['column_selection'][i] = var

        cols_canvas.pack(side="left", fill="both", expand=True)
        cols_scrollbar.pack(side="right", fill="y")

        # Rows tab
        rows_frame = ttk.Frame(selection_notebook)
        selection_notebook.add(rows_frame, text="Rows")

        ttk.Label(rows_frame, text=f"{action_text} rows:").pack(anchor='w')

        # Create scrollable frame for rows
        rows_canvas = tk.Canvas(rows_frame, height=150)
        rows_scrollbar = ttk.Scrollbar(rows_frame, orient="vertical", command=rows_canvas.yview)
        rows_scrollable = ttk.Frame(rows_canvas)

        rows_scrollable.bind("<Configure>", lambda e: rows_canvas.configure(scrollregion=rows_canvas.bbox("all")))
        rows_canvas.create_window((0, 0), window=rows_scrollable, anchor="nw")
        rows_canvas.configure(yscrollcommand=rows_scrollbar.set)

        self.assignment_widgets['row_selection'] = {}
        for i in range(min(n_rows, 100)):  # Limit to first 100 rows for performance
            var = tk.BooleanVar()
            if selection_method == 'include':
                var.set(True)  # Default include all
            ttk.Checkbutton(rows_scrollable, text=f"Row {i}", variable=var).pack(anchor='w')
            self.assignment_widgets['row_selection'][i] = var

        rows_canvas.pack(side="left", fill="both", expand=True)
        rows_scrollbar.pack(side="right", fill="y")

    def update_x_selection(self):
        """Update X selection options for 1D table mode."""
        x_frame = self.assignment_widgets['x_selection_frame']

        # Clear existing widgets
        for widget in x_frame.winfo_children():
            widget.destroy()

        x_source = self.assignment_widgets['x_source'].get()

        if x_source == 'none':
            return

        columns = list(self.parsed_data.columns)
        n_rows = len(self.parsed_data)

        if x_source == 'column':
            ttk.Label(x_frame, text="X column:").pack(side='left')
            self.assignment_widgets['x_index'] = tk.StringVar(value='0')
            values = [f"{col} (Col {i})" for i, col in enumerate(columns)]
            combo = ttk.Combobox(x_frame, textvariable=self.assignment_widgets['x_index'],
                                 values=values, state='readonly', width=20)
            combo.pack(side='left', padx=5)
            combo.bind('<<ComboboxSelected>>', lambda e: self.assignment_widgets['x_index'].set(
                str(combo.current())))
        else:  # row
            ttk.Label(x_frame, text="X row:").pack(side='left')
            self.assignment_widgets['x_index'] = tk.StringVar(value='0')
            spin = ttk.Spinbox(x_frame, textvariable=self.assignment_widgets['x_index'],
                               from_=0, to=n_rows - 1, width=10)
            spin.pack(side='left', padx=5)

    def update_source_options(self):
        """Update source selection options for 1D list mode."""
        source_frame = self.assignment_widgets['source_frame']

        # Clear existing widgets
        for widget in source_frame.winfo_children():
            widget.destroy()

        source_type = self.assignment_widgets['source_type'].get()
        columns = list(self.parsed_data.columns)
        n_rows = len(self.parsed_data)

        if source_type == 'column':
            ttk.Label(source_frame, text="Select column:").pack(side='left')
            self.assignment_widgets['source_index'] = tk.StringVar(value='0')
            values = [f"{col} (Col {i})" for i, col in enumerate(columns)]
            combo = ttk.Combobox(source_frame, textvariable=self.assignment_widgets['source_index'],
                                 values=values, state='readonly', width=20)
            combo.pack(side='left', padx=5)
            combo.bind('<<ComboboxSelected>>', lambda e: self.assignment_widgets['source_index'].set(
                str(combo.current())))
        else:  # row
            ttk.Label(source_frame, text="Select row:").pack(side='left')
            self.assignment_widgets['source_index'] = tk.StringVar(value='0')
            spin = ttk.Spinbox(source_frame, textvariable=self.assignment_widgets['source_index'],
                               from_=0, to=n_rows - 1, width=10)
            spin.pack(side='left', padx=5)

    def setup_2d_assignment(self, columns, n_rows, n_cols):
        """Setup 2D data assignment options."""
        # Multiple measurements option
        multi_frame = ttk.LabelFrame(self.assignment_frame, text="Measurement Type", padding=10)
        multi_frame.pack(fill='x', pady=5)

        self.assignment_widgets['multiple_measurements'] = tk.BooleanVar()
        ttk.Checkbutton(multi_frame, text="Multiple measurements (separate datasets from each Y column)",
                        variable=self.assignment_widgets['multiple_measurements'],
                        command=self.update_2d_options).pack(anchor='w')

        ttk.Label(multi_frame, text="Check this if each column represents a separate measurement/sample",
                  font=('TkDefaultFont', 8), foreground='gray').pack(anchor='w')

        # Assignment frame
        self.assignment_widgets['assign_frame'] = ttk.LabelFrame(self.assignment_frame,
                                                                 text="Column Assignment", padding=10)
        self.assignment_widgets['assign_frame'].pack(fill='x', pady=5)

        self.update_2d_options()

        # Column naming section for 2D
        self.add_column_naming_section("2D")

    def update_2d_options(self):
        """Update 2D assignment options."""
        assign_frame = self.assignment_widgets['assign_frame']

        # Clear existing widgets
        for widget in assign_frame.winfo_children():
            widget.destroy()

        columns = list(self.parsed_data.columns)
        multiple = self.assignment_widgets['multiple_measurements'].get()

        # X column selection
        ttk.Label(assign_frame, text="X column:").grid(row=0, column=0, sticky='w', pady=2)
        self.assignment_widgets['x_column'] = tk.StringVar(value=columns[0] if columns else '')
        x_combo = ttk.Combobox(assign_frame, textvariable=self.assignment_widgets['x_column'],
                               values=columns, state='readonly', width=20)
        x_combo.grid(row=0, column=1, sticky='w', pady=2, padx=5)

        # Y columns selection
        if multiple:
            ttk.Label(assign_frame, text="Y columns\n(each creates separate dataset):").grid(
                row=1, column=0, sticky='nw', pady=2)
        else:
            ttk.Label(assign_frame, text="Y columns:").grid(row=1, column=0, sticky='nw', pady=2)

        y_frame = ttk.Frame(assign_frame)
        y_frame.grid(row=1, column=1, sticky='w', pady=2, padx=5)

        self.assignment_widgets['y_columns'] = {}
        for i, col in enumerate(columns):
            var = tk.BooleanVar()
            if i > 0:  # Default: select all except first (assuming first is X)
                var.set(True)
            ttk.Checkbutton(y_frame, text=f"{col} (Col {i})", variable=var).pack(anchor='w')
            self.assignment_widgets['y_columns'][col] = var

        # Warning for single column files
        if len(columns) < 2:
            ttk.Label(assign_frame, text="⚠ Warning: 2D data requires at least 2 columns",
                      foreground='red').grid(row=2, column=0, columnspan=2, sticky='w', pady=5)

    def setup_3d_assignment(self, columns, n_rows, n_cols):
        """Setup 3D data assignment options."""
        # Information label
        info_frame = ttk.Frame(self.assignment_frame)
        info_frame.pack(fill='x', pady=5)

        ttk.Label(info_frame,
                  text="3D Data: X and Y can only be assigned from first row or first column. Z will be the remaining 2D data matrix.",
                  font=('TkDefaultFont', 9), foreground='blue').pack(anchor='w')

        ttk.Label(info_frame, text="Note: Headers/text labels will be automatically excluded from numerical data.",
                  font=('TkDefaultFont', 8), foreground='gray').pack(anchor='w')

        # 3D Data Format Selection
        format_frame = ttk.LabelFrame(self.assignment_frame, text="3D Data Format", padding=10)
        format_frame.pack(fill='x', pady=5)

        self.assignment_widgets['data_format'] = tk.StringVar(value='array')

        ttk.Radiobutton(format_frame, text="Array-shaped (X,Y from edges, Z as 2D matrix)",
                       variable=self.assignment_widgets['data_format'], value='array').pack(anchor='w')
        ttk.Radiobutton(format_frame, text="Flattened table (X,Y,Z columns)",
                       variable=self.assignment_widgets['data_format'], value='flattened').pack(anchor='w')

        ttk.Label(format_frame, text="Note: All 3D data will be converted to flattened format for plotting",
                  font=('TkDefaultFont', 8), foreground='gray').pack(anchor='w', pady=(5,0))

        # X assignment - restricted to first row or first column only
        x_frame = ttk.LabelFrame(self.assignment_frame, text="X Data Assignment", padding=10)
        x_frame.pack(fill='x', pady=5)

        ttk.Label(x_frame, text="X source (first row/column only):").grid(row=0, column=0, sticky='w')
        self.assignment_widgets['x_type'] = tk.StringVar(value='column')

        # Only allow first column or first row
        ttk.Radiobutton(x_frame, text="First Column", variable=self.assignment_widgets['x_type'],
                        value='column', command=self.update_3d_x_options).grid(row=0, column=1, sticky='w')
        ttk.Radiobutton(x_frame, text="First Row", variable=self.assignment_widgets['x_type'],
                        value='row', command=self.update_3d_x_options).grid(row=0, column=2, sticky='w')

        self.assignment_widgets['x_frame'] = ttk.Frame(x_frame)
        self.assignment_widgets['x_frame'].grid(row=1, column=0, columnspan=3, sticky='ew', pady=5)

        # Column naming section for 3D
        self.add_column_naming_section("3D")

        # Add skip header option for 3D (applies to both X and Y)
        skip_frame = ttk.Frame(self.assignment_frame)
        skip_frame.pack(fill='x', pady=5)

        self.assignment_widgets['skip_first_cell'] = tk.BooleanVar(value=True)
        ttk.Checkbutton(skip_frame, text="Skip first cell for both X and Y coordinates (header/label)",
                        variable=self.assignment_widgets['skip_first_cell']).pack(anchor='w')

        # Y assignment - restricted to first row or first column only
        y_frame = ttk.LabelFrame(self.assignment_frame, text="Y Data Assignment", padding=10)
        y_frame.pack(fill='x', pady=5)

        ttk.Label(y_frame, text="Y source (first row/column only):").grid(row=0, column=0, sticky='w')
        self.assignment_widgets['y_type'] = tk.StringVar(value='row')  # Default to row if X is column

        # Only allow first column or first row
        ttk.Radiobutton(y_frame, text="First Column", variable=self.assignment_widgets['y_type'],
                        value='column', command=self.update_3d_y_options).grid(row=0, column=1, sticky='w')
        ttk.Radiobutton(y_frame, text="First Row", variable=self.assignment_widgets['y_type'],
                        value='row', command=self.update_3d_y_options).grid(row=0, column=2, sticky='w')

        self.assignment_widgets['y_frame'] = ttk.Frame(y_frame)
        self.assignment_widgets['y_frame'].grid(row=1, column=0, columnspan=3, sticky='ew', pady=5)

        # Add skip header option for Y
        self.assignment_widgets['y_skip_header'] = tk.BooleanVar(value=True)
        """ttk.Checkbutton(y_frame, text="Skip first cell (header/label)",
                        variable=self.assignment_widgets['y_skip_header']).grid(row=2, column=0, columnspan=3,
                                                                                sticky='w')
"""
        # Z info - clarify that it's a 2D matrix
        z_frame = ttk.LabelFrame(self.assignment_frame, text="Z Data Matrix (Automatic)", padding=10)
        z_frame.pack(fill='x', pady=5)

        ttk.Label(z_frame, text="Z data will be a 2D matrix containing all remaining numerical data",
                  font=('TkDefaultFont', 8), foreground='gray').pack(anchor='w')
        ttk.Label(z_frame, text="(excluding the selected X and Y vectors and any headers/labels)",
                  font=('TkDefaultFont', 8), foreground='gray').pack(anchor='w')

        # Add validation warning
        validation_frame = ttk.Frame(self.assignment_frame)
        validation_frame.pack(fill='x', pady=5)

        self.assignment_widgets['validation_label'] = ttk.Label(validation_frame, text="",
                                                                font=('TkDefaultFont', 8), foreground='red')
        self.assignment_widgets['validation_label'].pack(anchor='w')

        self.update_3d_x_options()
        self.update_3d_y_options()

    def update_3d_x_options(self):
        """Update X assignment options for 3D data - restricted to first row/column only."""
        if not hasattr(self, 'assignment_widgets') or 'x_frame' not in self.assignment_widgets:
            return

        # Clear existing widgets
        for widget in self.assignment_widgets['x_frame'].winfo_children():
            widget.destroy()

        if self.parsed_data is None:
            return

        x_type = self.assignment_widgets['x_type'].get()

        # Since we only allow first row/column, no selection needed - just show info
        if x_type == 'column':
            ttk.Label(self.assignment_widgets['x_frame'],
                      text="X will be taken from first column (column 0)",
                      font=('TkDefaultFont', 8)).pack(anchor='w')
            # Show preview of first column values (excluding potential header)
            try:
                if len(self.parsed_data) > 0:
                    first_col = [row[0] if len(row) > 0 else '' for row in self.parsed_data]
                    preview_vals = first_col[1:6] if len(first_col) > 1 else first_col[:5]  # Skip potential header
                    preview_text = ", ".join([str(v) for v in preview_vals if str(v).strip()])
                    if len(first_col) > 6:
                        preview_text += "..."
                    ttk.Label(self.assignment_widgets['x_frame'],
                              text=f"Preview: {preview_text}",
                              font=('TkDefaultFont', 8), foreground='blue').pack(anchor='w')
            except Exception:
                pass
        else:  # row
            ttk.Label(self.assignment_widgets['x_frame'],
                      text="X will be taken from first row (row 0)",
                      font=('TkDefaultFont', 8)).pack(anchor='w')
            # Show preview of first row values (excluding potential header)
            try:
                if len(self.parsed_data) > 0 and len(self.parsed_data[0]) > 0:
                    first_row = self.parsed_data[0]
                    preview_vals = first_row[1:6] if len(first_row) > 1 else first_row[:5]  # Skip potential header
                    preview_text = ", ".join([str(v) for v in preview_vals if str(v).strip()])
                    if len(first_row) > 6:
                        preview_text += "..."
                    ttk.Label(self.assignment_widgets['x_frame'],
                              text=f"Preview: {preview_text}",
                              font=('TkDefaultFont', 8), foreground='blue').pack(anchor='w')
            except Exception:
                pass

        self.validate_3d_assignment()

    def update_3d_y_options(self):
        """Update Y assignment options for 3D data - restricted to first row/column only."""
        if not hasattr(self, 'assignment_widgets') or 'y_frame' not in self.assignment_widgets:
            return

        # Clear existing widgets
        for widget in self.assignment_widgets['y_frame'].winfo_children():
            widget.destroy()

        if self.parsed_data is None:
            return

        y_type = self.assignment_widgets['y_type'].get()

        # Since we only allow first row/column, no selection needed - just show info
        if y_type == 'column':
            ttk.Label(self.assignment_widgets['y_frame'],
                      text="Y will be taken from first column (column 0)",
                      font=('TkDefaultFont', 8)).pack(anchor='w')
            # Show preview of first column values (excluding potential header)
            try:
                if len(self.parsed_data) > 0:
                    first_col = [row[0] if len(row) > 0 else '' for row in self.parsed_data]
                    preview_vals = first_col[1:6] if len(first_col) > 1 else first_col[:5]  # Skip potential header
                    preview_text = ", ".join([str(v) for v in preview_vals if str(v).strip()])
                    if len(first_col) > 6:
                        preview_text += "..."
                    ttk.Label(self.assignment_widgets['y_frame'],
                              text=f"Preview: {preview_text}",
                              font=('TkDefaultFont', 8), foreground='blue').pack(anchor='w')
            except Exception:
                pass
        else:  # row
            ttk.Label(self.assignment_widgets['y_frame'],
                      text="Y will be taken from first row (row 0)",
                      font=('TkDefaultFont', 8)).pack(anchor='w')
            # Show preview of first row values (excluding potential header)
            try:
                if len(self.parsed_data) > 0 and len(self.parsed_data[0]) > 0:
                    first_row = self.parsed_data[0]
                    preview_vals = first_row[1:6] if len(first_row) > 1 else first_row[:5]  # Skip potential header
                    preview_text = ", ".join([str(v) for v in preview_vals if str(v).strip()])
                    if len(first_row) > 6:
                        preview_text += "..."
                    ttk.Label(self.assignment_widgets['y_frame'],
                              text=f"Preview: {preview_text}",
                              font=('TkDefaultFont', 8), foreground='blue').pack(anchor='w')
            except Exception:
                pass

        self.validate_3d_assignment()

    def validate_3d_assignment(self):
        """Validate 3D assignment configuration and show warnings."""
        if not hasattr(self, 'assignment_widgets') or 'validation_label' not in self.assignment_widgets:
            return

        x_type = self.assignment_widgets['x_type'].get()
        y_type = self.assignment_widgets['y_type'].get()

        validation_msg = ""

        # Check for conflicting assignments
        if x_type == y_type:
            if x_type == 'column':
                validation_msg = "⚠️ Warning: Both X and Y are assigned to first column. This will result in identical vectors."
            else:
                validation_msg = "⚠️ Warning: Both X and Y are assigned to first row. This will result in identical vectors."
        else:
            validation_msg = "✓ Valid assignment: X and Y will be different vectors"
            self.assignment_widgets['validation_label'].config(foreground='green')

        self.assignment_widgets['validation_label'].config(text=validation_msg)
        if validation_msg.startswith("⚠️"):
            self.assignment_widgets['validation_label'].config(foreground='red')

    def collect_3d_assignments(self):
        """Collect 3D assignment information with improved data handling."""
        if not hasattr(self, 'assignment_widgets'):
            return {}

        assignments = {
            'data_type': '3D',
            'x_type': self.assignment_widgets['x_type'].get(),
            'y_type': self.assignment_widgets['y_type'].get(),
            'x_skip_header': self.assignment_widgets.get('x_skip_header', tk.BooleanVar(value=True)).get(),
            'y_skip_header': self.assignment_widgets.get('x_skip_header', tk.BooleanVar(value=True)).get(),
            'x_index': 0,  # Always first row/column
            'y_index': 0  # Always first row/column
        }

        return assignments

    """def extract_3d_data(self, data, assignments):
        """"""Extract 3D data with proper handling of headers and 2D Z matrix."""""""
        import numpy as np

        x_type = assignments['x_type']
        y_type = assignments['y_type']
        x_skip_header = assignments.get('x_skip_header', True)
        y_skip_header = assignments.get('y_skip_header', True)

        # Convert data to numpy array for easier manipulation
        try:
            # First, let's identify numerical vs text data
            data_array = np.array(data, dtype=object)

            # Extract X values
            if x_type == 'column':
                x_raw = data_array[:, 0] if data_array.shape[1] > 0 else []
                if x_skip_header and len(x_raw) > 0:
                    x_raw = x_raw[1:]  # Skip first element (header)
            else:  # row
                x_raw = data_array[0, :] if data_array.shape[0] > 0 else []
                if x_skip_header and len(x_raw) > 0:
                    x_raw = x_raw[1:]  # Skip first element (header)

            # Extract Y values
            if y_type == 'column':
                y_raw = data_array[:, 0] if data_array.shape[1] > 0 else []
                if y_skip_header and len(y_raw) > 0:
                    y_raw = y_raw[1:]  # Skip first element (header)
            else:  # row
                y_raw = data_array[0, :] if data_array.shape[0] > 0 else []
                if y_skip_header and len(y_raw) > 0:
                    y_raw = y_raw[1:]  # Skip first element (header)

            # Convert to numerical values, filtering out non-numeric entries
            def to_numeric_array(raw_data):
                numeric_values = []
                for val in raw_data:
                    try:
                        if isinstance(val, (int, float)):
                            numeric_values.append(float(val))
                        elif isinstance(val, str) and val.strip():
                            # Try to convert string to number
                            cleaned = val.strip().replace(',', '.')  # Handle different decimal separators
                            numeric_values.append(float(cleaned))
                    except (ValueError, TypeError):
                        # Skip non-numeric values (headers, text labels, etc.)
                        continue
                return np.array(numeric_values)

            x_values = to_numeric_array(x_raw)
            y_values = to_numeric_array(y_raw)

            # Extract Z matrix (all remaining numerical data)
            # Determine which rows/columns to exclude based on X and Y assignments
            z_data = data_array.copy()

            # Remove X and Y source rows/columns and headers
            if x_type == 'column' and y_type == 'row':
                # Remove first column (X) and first row (Y)
                z_data = z_data[1:, 1:] if z_data.shape[0] > 1 and z_data.shape[1] > 1 else z_data
            elif x_type == 'row' and y_type == 'column':
                # Remove first row (X) and first column (Y)
                z_data = z_data[1:, 1:] if z_data.shape[0] > 1 and z_data.shape[1] > 1 else z_data
            elif x_type == 'column' and y_type == 'column':
                # Both from same column - remove first column and optionally first row if it contains headers
                z_data = z_data[:, 1:] if z_data.shape[1] > 1 else z_data
                if x_skip_header or y_skip_header:
                    z_data = z_data[1:, :] if z_data.shape[0] > 1 else z_data
            elif x_type == 'row' and y_type == 'row':
                # Both from same row - remove first row and optionally first column if it contains headers
                z_data = z_data[1:, :] if z_data.shape[0] > 1 else z_data
                if x_skip_header or y_skip_header:
                    z_data = z_data[:, 1:] if z_data.shape[1] > 1 else z_data

            # Convert Z data to numerical 2D array
            z_matrix = []
            for row in z_data:
                numeric_row = []
                for val in row:
                    try:
                        if isinstance(val, (int, float)):
                            numeric_row.append(float(val))
                        elif isinstance(val, str) and val.strip():
                            cleaned = val.strip().replace(',', '.')
                            numeric_row.append(float(cleaned))
                        else:
                            numeric_row.append(np.nan)  # Use NaN for missing/invalid data
                    except (ValueError, TypeError):
                        numeric_row.append(np.nan)
                if numeric_row:  # Only add non-empty rows
                    z_matrix.append(numeric_row)

            z_values = np.array(z_matrix) if z_matrix else np.array([[]])

            return {
                'x': x_values,
                'y': y_values,
                'z': z_values,  # Now a 2D matrix instead of a vector
                'x_type': x_type,
                'y_type': y_type,
                'shape': z_values.shape
            }

        except Exception as e:
            raise ValueError(f"Error extracting 3D data: {str(e)}")

    def preview_3d_data(self, extracted_data):
        """""""Generate preview text for 3D data showing proper structure.""""""
        if not extracted_data:
            return "No data to preview"

        x_vals = extracted_data.get('x', [])
        y_vals = extracted_data.get('y', [])
        z_matrix = extracted_data.get('z', np.array([[]]))

        preview_lines = []
        preview_lines.append("=== 3D Data Preview ===")
        preview_lines.append(f"X values ({len(x_vals)} points): {x_vals[:5]}{'...' if len(x_vals) > 5 else ''}")
        preview_lines.append(f"Y values ({len(y_vals)} points): {y_vals[:5]}{'...' if len(y_vals) > 5 else ''}")
        preview_lines.append(f"Z matrix shape: {z_matrix.shape}")

        if z_matrix.size > 0:
            preview_lines.append("Z matrix preview (first 3x3):")
            rows_to_show = min(3, z_matrix.shape[0])
            cols_to_show = min(3, z_matrix.shape[1])
            for i in range(rows_to_show):
                row_preview = []
                for j in range(cols_to_show):
                    val = z_matrix[i, j]
                    if np.isnan(val):
                        row_preview.append("NaN")
                    else:
                        row_preview.append(f"{val:.3f}")
                if z_matrix.shape[1] > cols_to_show:
                    row_preview.append("...")
                preview_lines.append("  " + " | ".join(row_preview))
            if z_matrix.shape[0] > rows_to_show:
                preview_lines.append("  ...")
        else:
            preview_lines.append("Z matrix: No numerical data found")

        return "\n".join(preview_lines)"""

    def detect_data_dimensionality(self):
        """Detect dimensionality based on current data assignments."""
        try:
            # Get current assignments from the UI
            current_page = self.notebook.index(self.notebook.select())

            if current_page == 2:  # Assignment page
                data_type = self.data_type_var.get()

                if data_type == "1D":
                    return 1
                elif data_type == "2D":
                    return 2
                elif data_type == "3D":
                    return 3
                else:
                    # Fallback: analyze data structure
                    return self._analyze_data_structure()
            else:
                # If not on assignment page, analyze data structure
                return self._analyze_data_structure()

        except Exception as e:
            print(f"Warning: Failed to detect dimensionality: {e}")
            return self._analyze_data_structure()

    def _analyze_data_structure(self):
        """Analyze data structure to determine dimensionality."""
        if self.parsed_data is None or self.parsed_data.empty:
            return 2  # Default fallback

        rows, cols = self.parsed_data.shape

        # Simple heuristics for dimensionality detection
        if cols <= 2:
            return 1  # Likely 1D data (index + value or x + y)
        elif cols <= 4:
            return 2  # Likely 2D data (x + multiple y columns)
        else:
            return 3  # Likely 3D data (x + y + multiple z columns)

    def create_plot_config(self, dimension):
        """Create initial plot configuration based on dimensionality."""
        import datetime

        base_config = {
            "dimension": dimension,
            "created_date": datetime.datetime.now().isoformat(),
            "import_source": self.current_file,
            "import_settings": {
                "delimiter": self.delimiter_var.get(),
                "decimal_separator": self.decimal_var.get(),
                "has_header": self.has_header_var.get(),
                "header_line": self.header_var.get(),
                "data_start": self.data_start_var.get()
            }
        }

        if dimension == 1:
            # 1D plot configuration defaults
            plot_config = {
                **base_config,
                "x_label": "Index",
                "y1_label": "Value",
                "title": f"1D Plot - {Path(self.current_file).stem}",
                "grid": True,
                "legend_position": "best",
                "font": "Arial",
                "fontsize": 12,
                "ticksize": 10,
                "title_size": 14,
                "legend_size": 10,
                "marker": "None",
                "line_style": "-",
                "line_width": 1.0,
                "log_x": False,
                "log_y": False
            }
        elif dimension == 2:
            # 2D plot configuration defaults
            plot_config = {
                **base_config,
                "x_label": "X",
                "y1_label": "Y",
                "y2_label": "",
                "title": f"2D Plot - {Path(self.current_file).stem}",
                "x_lim": ["", ""],
                "y1_lim": ["", ""],
                "y2_lim": ["", ""],
                "grid": True,
                "legend_position": "best",
                "font": "Arial",
                "fontsize": 12,
                "ticksize": 10,
                "title_size": 14,
                "legend_size": 10,
                "x_tick_spacing": "",
                "y_tick_spacing": "",
                "y2_tick_spacing": "",
                "x_opposite_ticks": False,
                "y_opposite_ticks": False,
                "y2_opposite_ticks": False,
                "x_scientific_notation": False,
                "y_scientific_notation": False,
                "y2_scientific_notation": False,
                "marker": "None",
                "line_style": "-",
                "line_width": 1.0,
                "color_palette_name": "tab10",
                "log_x": False,
                "log_y": False,
                "log_y2": False
            }
        else:  # dimension == 3
            # 3D plot configuration defaults
            plot_config = {
                **base_config,
                "x_label": "X",
                "y_label": "Y",
                "z_label": "Z",
                "title": f"3D Plot - {Path(self.current_file).stem}",
                "plot_type": "surface",
                "colormap": "viridis",
                "alpha": 1.0,
                "linewidth": 1.0,
                "colorbar": True,
                "elevation": 30.0,
                "azimuth": -60.0,
                "font": "Arial",
                "fontsize": 12,
                "ticksize": 10,
                "log_x": False,
                "log_y": False,
                "log_z": False
            }

        return plot_config

    def process_data_for_plotting(self):
        """Process data based on assignments and create export_df without saving files.
        Handles multi-file processing according to user requirements:
        - 2D data: Combine all files into one dataset
        - 3D data: Create separate datasets per file
        """
        try:
            # Collect assignments
            assignments = self.collect_column_assignments()
            data_type = assignments.get('data_type')

            if not data_type:
                messagebox.showerror("Error", "Please specify data type and assignments")
                return None, None

            # Check if we have multiple files to process
            if len(self.current_files) > 1:
                # Multi-file processing
                result = self._process_multi_file_data(assignments, data_type)
            else:
                # Single file processing (existing logic)
                if self.parsed_data is None or self.parsed_data.empty:
                    messagebox.showerror("Error", "No data to process")
                    return None, None

                # Process data based on type
                if data_type == '1D':
                    result = self._process_1d_data(assignments)
                elif data_type == '2D':
                    result = self._process_2d_data(assignments)
                elif data_type == '3D':
                    result = self._process_3d_data(assignments)
                else:
                    messagebox.showerror("Error", f"Unsupported data type: {data_type}")
                    return None, None

            # Return result and dimension
            # Result can be a single DataFrame or list of DataFrames (for multiple datasets)
            return result, int(data_type[0])

        except Exception as e:
            messagebox.showerror("Error", f"Failed to process data: {str(e)}")
            return None, None

    def _process_multi_file_data(self, assignments, data_type):
        """Process multiple files according to user requirements:
        - 2D data: Combine all files into one dataset
        - 3D data: Create separate datasets per file
        """
        datasets = []

        # Get current parsing parameters (same as single-file parsing)
        delimiter = self.delimiter_var.get().replace('\\t', '\t')
        decimal_sep = self.decimal_var.get()

        # Handle header detection (convert from 1-based user input to 0-based internal)
        if self.has_header_var.get():
            header_line = int(self.header_var.get()) if self.header_var.get() != '-1' else -1
        else:
            header_line = -1

        data_start = int(self.data_start_var.get())  # Convert from 1-based to 0-based

        # Cache parsed files to avoid re-parsing
        parsed_files = {}

        for file_path in self.current_files:
            try:
                # Check if we already parsed this file
                if file_path in parsed_files:
                    file_data = parsed_files[file_path]
                else:
                    # Parse file only once and cache it
                    file_analysis = self.file_analyses.get(file_path) if self.file_analyses else None
                    file_data = self.parser.parse_file(
                        file_path, delimiter, decimal_sep, header_line, data_start,
                        file_analysis=file_analysis
                    )
                    parsed_files[file_path] = file_data

                if file_data.empty:
                    print(f"Warning: File {file_path} is empty, skipping")
                    continue

                # Store original parsed_data and temporarily replace it
                original_parsed_data = self.parsed_data
                self.parsed_data = file_data

                # Process this file's data
                if data_type == '1D':
                    file_result = self._process_1d_data(assignments)
                elif data_type == '2D':
                    file_result = self._process_2d_data(assignments)
                elif data_type == '3D':
                    file_result = self._process_3d_data(assignments)
                else:
                    raise ValueError(f"Unsupported data type: {data_type}")

                # Restore original parsed_data
                self.parsed_data = original_parsed_data

                # Handle the result - for 3D data, each file should produce exactly one DataFrame
                if data_type == '3D':
                    # 3D data: Each file = one complete 3D dataset (one DataFrame)
                    if isinstance(file_result, list):
                        raise ValueError(f"3D data processing should return single DataFrame, got list from {file_path}")

                    # Single DataFrame representing one complete 3D dataset
                    file_name = Path(file_path).stem
                    file_result.name = file_name
                    datasets.append(file_result)
                else:
                    # 2D/1D data: Handle as before
                    if isinstance(file_result, list):
                        # Multiple datasets from one file
                        for i, dataset in enumerate(file_result):
                            file_name = Path(file_path).stem
                            dataset.name = f"{file_name}_Dataset_{i+1}"
                            datasets.append(dataset)
                    else:
                        # Single dataset from file
                        file_name = Path(file_path).stem
                        file_result.name = file_name
                        datasets.append(file_result)

            except Exception as e:
                print(f"Error processing file {file_path}: {e}")
                import traceback
                traceback.print_exc()  # Print full traceback for debugging
                continue

        if not datasets:
            raise ValueError("No valid data could be processed from any file")

        # Apply user requirements for multi-file handling
        if data_type == '2D':
            # 2D data: Combine all datasets into one
            if len(datasets) == 1:
                return datasets[0]  # Single dataset
            else:
                # Combine all 2D datasets - return as list for multi-dataset 2D plotting
                return datasets
        elif data_type == '3D':
            # 3D data: Return list of DataFrames (one per file)
            # Each DataFrame represents one complete 3D dataset
            # Plotting app will show first DataFrame and allow navigation through the list
            return datasets
        else:
            # 1D data: Keep separate datasets per file
            return datasets

    def _process_1d_data(self, assignments):
        """Process 1D data and return DataFrame."""
        subtype = assignments.get('subtype', 'list')

        if subtype == 'list':
            # Single list from column or row
            source_type = assignments.get('source_type', 'column')
            source_index = assignments.get('source_index', 0)

            if source_type == 'column':
                data = self.parsed_data.iloc[:, source_index]
            else:  # row
                data = self.parsed_data.iloc[source_index, :]

            # Create DataFrame with custom column names
            x_name = assignments.get('x_name', 'Index')
            y_name = assignments.get('y_name', 'Value')

            export_df = pd.DataFrame({
                x_name: range(len(data)),
                y_name: data.values
            })

        elif subtype == 'multiple':
            # Multiple datasets from selected columns/rows
            x_source = assignments.get('x_source', 'none')
            x_index = assignments.get('x_index', 0)
            selected_cols = assignments.get('selected_columns', [])
            selected_rows = assignments.get('selected_rows', [])

            # Get X data if specified
            x_data = None
            if x_source == 'column' and x_index < len(self.parsed_data.columns):
                x_data = self.parsed_data.iloc[:, x_index]
            elif x_source == 'row' and x_index < len(self.parsed_data):
                x_data = self.parsed_data.iloc[x_index, :]

            # Process data - for multiple 1D, we'll combine all into one DataFrame
            all_data = []

            if selected_cols and selected_rows:
                data_subset = self.parsed_data.iloc[selected_rows, :]
                data_subset = data_subset.iloc[:, selected_cols]

                # Get custom column names
                x_name = assignments.get('x_name', 'X')
                y_name = assignments.get('y_name', 'Y')

                for col_idx, col_pos in enumerate(selected_cols):
                    y_data = data_subset.iloc[:, col_idx]

                    if x_data is not None and x_source == 'column':
                        for i, (x_val, y_val) in enumerate(zip(x_data.values, y_data.values)):
                            all_data.append({x_name: x_val, y_name: y_val, 'Dataset': f'Dataset_{col_idx+1}'})
                    elif x_data is not None and x_source == 'row':
                        x_val = x_data.iloc[col_idx] if col_idx < len(x_data) else x_data.iloc[0]
                        for i, y_val in enumerate(y_data.values):
                            all_data.append({x_name: x_val, y_name: y_val, 'Dataset': f'Dataset_{col_idx+1}'})
                    else:
                        for i, y_val in enumerate(y_data.values):
                            all_data.append({x_name: i, y_name: y_val, 'Dataset': f'Dataset_{col_idx+1}'})

            export_df = pd.DataFrame(all_data)

        return export_df

    def _process_2d_data(self, assignments):
        """Process 2D data and return DataFrame."""
        x_column = assignments.get('x_column')
        y_columns = assignments.get('y_columns', [])
        multiple_measurements = assignments.get('multiple_measurements', False)

        if not x_column or not y_columns:
            raise ValueError("X column and Y columns must be specified for 2D data")

        x_data = self.parsed_data[x_column]

        # For 2D data, always create a single measurement with all Y columns as separate signals
        # This matches the user requirement that multiple Y columns should be individual signals

        # First, collect all valid Y columns
        valid_y_columns = []
        for y_col in y_columns:
            if y_col in self.parsed_data.columns:
                valid_y_columns.append(y_col)

        if not valid_y_columns:
            raise ValueError("No valid Y columns found")

        # Create combined data dictionary with X and all Y signals
        x_name = assignments.get('x_name', 'X')
        combined_data = {x_name: x_data}

        # Add all Y columns as separate signals using their original column names
        for y_col in valid_y_columns:
            y_data = self.parsed_data[y_col]
            # Use original column name to preserve signal identity
            combined_data[y_col] = y_data

        export_df = pd.DataFrame(combined_data)

        return export_df

    def _process_3d_data(self, assignments):
        """Process 3D data and return a single DataFrame representing one complete 3D dataset.

        For 3D data:
        - Each file = one complete 3D dataset (one DataFrame)
        - DataFrame format: X, Y, Z (where Z is the primary/first Z signal)
        - PlottingApp3D will handle this as one complete 3D plot
        """
        data_format = assignments.get('data_format', 'array')

        # Get custom column names
        x_name = assignments.get('x_name', 'X')
        y_name = assignments.get('y_name', 'Y')
        z_name = assignments.get('z_name', 'Z')

        if data_format == 'flattened':
            # Handle flattened table format (X, Y, Z columns)
            x_column = assignments.get('x_column')
            y_column = assignments.get('y_column')
            z_columns = assignments.get('z_columns', [])

            if not x_column or not y_column or not z_columns:
                raise ValueError("X, Y, and Z columns must be specified for flattened 3D data")

            # Create ONE complete 3D dataset with X, Y, and primary Z
            # Use the first Z column as the primary Z signal for this dataset
            primary_z_column = z_columns[0]

            result_data = {
                x_name: self.parsed_data[x_column],
                y_name: self.parsed_data[y_column],
                z_name: self.parsed_data[primary_z_column]  # Use first Z column as primary
            }

            # Create single DataFrame representing one complete 3D dataset
            dataset_df = pd.DataFrame(result_data)

            # Remove rows where X, Y, or Z are NaN
            dataset_df = dataset_df.dropna(subset=[x_name, y_name, z_name])

            if dataset_df.empty:
                raise ValueError("No valid data could be processed for 3D export")

            # Return single DataFrame - this is ONE complete 3D dataset with X, Y, Z
            return dataset_df

        else:  # array format
            # Handle array-shaped format (X,Y from edges, Z as 2D matrix)
            x_type = assignments.get('x_type', 'column')
            y_type = assignments.get('y_type', 'column')
            x_index = assignments.get('x_index', 0)
            y_index = assignments.get('y_index', 0)

            # ENHANCED DEBUG: Show data structure before extraction
            print(f"Debug: Data structure analysis:")
            print(f"  Original data shape: {self.parsed_data.shape}")
            print(f"  Extraction plan: X from {x_type} {x_index}, Y from {y_type} {y_index}")

            # Get X data (from first row or column)
            if x_type == 'column':
                if x_index >= len(self.parsed_data.columns):
                    raise ValueError(f"X column index {x_index} out of range")
                x_data = self.parsed_data.iloc[:, x_index]
                print(f"  X extracted from column {x_index}: {len(x_data)} values")
            else:  # row
                if x_index >= len(self.parsed_data):
                    raise ValueError(f"X row index {x_index} out of range")
                x_data = self.parsed_data.iloc[x_index, :]
                print(f"  X extracted from row {x_index}: {len(x_data)} values")

            # Get Y data (from first row or column)
            if y_type == 'column':
                if y_index >= len(self.parsed_data.columns):
                    raise ValueError(f"Y column index {y_index} out of range")
                y_data = self.parsed_data.iloc[:, y_index]
                print(f"  Y extracted from column {y_index}: {len(y_data)} values")
            else:  # row
                if y_index >= len(self.parsed_data):
                    raise ValueError(f"Y row index {y_index} out of range")
                y_data = self.parsed_data.iloc[y_index, :]
                print(f"  Y extracted from row {y_index}: {len(y_data)} values")

            # Get Z data - CRITICAL FIX for dimension alignment
            # For array format, Z matrix should represent data values at grid points
            # The matrix dimensions should match the coordinate grid formed by X and Y

            if x_type == 'column' and y_type == 'column':
                # X and Y are columns (coordinate vectors)
                # Z matrix should be the remaining data matrix
                z_cols = [i for i in range(len(self.parsed_data.columns)) if i not in [x_index, y_index]]
                z_data = self.parsed_data.iloc[:, z_cols]
                z_col_names = [self.parsed_data.columns[i] for i in z_cols]

            elif x_type == 'row' and y_type == 'row':
                # X and Y are rows (coordinate vectors)
                # Z matrix should be the remaining data matrix
                z_rows = [i for i in range(len(self.parsed_data)) if i not in [x_index, y_index]]
                z_data = self.parsed_data.iloc[z_rows, :]
                z_col_names = list(z_data.columns)

            elif x_type == 'column' and y_type == 'row':
                # X is column, Y is row - CRITICAL FIX
                # Problem: Z matrix extraction was removing coordinate headers incorrectly
                #
                # Data structure should be:
                # - X coordinates: column x_index (512 values)
                # - Y coordinates: row y_index (128 values)
                # - Z matrix: should be (128, 512) to match coordinate grid
                #
                # The issue: coordinate headers might be included in the coordinate count
                # but excluded from Z matrix, causing dimension mismatch

                print(f"Debug: Original data shape: {self.parsed_data.shape}")
                print(f"Debug: X column {x_index}, Y row {y_index}")

                # Extract Z matrix more carefully
                # Remove Y coordinate row first
                z_data = self.parsed_data.drop(self.parsed_data.index[y_index])
                print(f"Debug: After removing Y row: {z_data.shape}")

                # Remove X coordinate column
                z_data = z_data.drop(z_data.columns[x_index], axis=1)
                print(f"Debug: After removing X column: {z_data.shape}")

                z_col_names = list(z_data.columns)

            else:  # x_type == 'row' and y_type == 'column'
                # X is row, Y is column - special case
                # Z matrix is the intersection: exclude X row, exclude Y column
                z_data = self.parsed_data.drop(self.parsed_data.index[x_index])
                z_data = z_data.drop(z_data.columns[y_index], axis=1)
                z_col_names = list(z_data.columns)

            # For array format 3D data, create ONE complete 3D dataset
            # CRITICAL: Ensure Z matrix dimensions match X and Y coordinate arrays

            # Clean X and Y data (remove NaN values)
            print(f"Debug: Raw X data length: {len(x_data)}, Y data length: {len(y_data)}")
            print(f"Debug: X data sample: {x_data.head().values}")
            print(f"Debug: Y data sample: {y_data.head().values}")

            x_clean = x_data.dropna().values
            y_clean = y_data.dropna().values

            print(f"Debug: After dropna - X length: {len(x_clean)}, Y length: {len(y_clean)}")
            print(f"Debug: X clean sample: {x_clean[:5]}")
            print(f"Debug: Y clean sample: {y_clean[:5]}")

            # CRITICAL DIMENSION FIX: For array format, Z matrix must have proper dimensions
            # Z matrix should be (len(y_clean), len(x_clean)) to match coordinate grid

            # Get the original Z data matrix
            z_raw = z_data.values

            print(f"Debug: X length: {len(x_clean)}, Y length: {len(y_clean)}")
            print(f"Debug: Raw Z matrix shape: {z_raw.shape}")
            print(f"Debug: Extraction method: X from {x_type}, Y from {y_type}")

            # CORRECT LOGIC: The Z matrix dimensions should match the coordinate grid
            # For proper 3D plotting: Z[i,j] corresponds to point (X[j], Y[i])
            # So Z matrix should have shape (len(Y), len(X))

            expected_shape = (len(y_clean), len(x_clean))
            print(f"Debug: Expected Z shape: {expected_shape}")

            # COMPREHENSIVE DIMENSION CORRECTION
            # Handle all extraction scenarios properly

            if z_raw.shape == expected_shape:
                # Perfect match - no correction needed
                z_matrix = z_raw
                print("✓ Z matrix already has correct dimensions")

            elif z_raw.shape == (expected_shape[1], expected_shape[0]):
                # Dimensions are swapped - transpose fixes it
                z_matrix = z_raw.T
                print(f"✓ Applied transpose to fix swapped dimensions: {z_raw.shape} → {z_matrix.shape}")

            elif z_raw.size == expected_shape[0] * expected_shape[1]:
                # Same number of elements - reshape
                z_matrix = z_raw.reshape(expected_shape)
                print(f"✓ Applied reshape to match expected dimensions: {z_raw.shape} → {z_matrix.shape}")

            elif z_raw.shape[0] >= expected_shape[0] and z_raw.shape[1] >= expected_shape[1]:
                # Matrix is larger - crop to expected size
                z_matrix = z_raw[:expected_shape[0], :expected_shape[1]]
                print(f"✓ Applied crop to match expected dimensions: {z_raw.shape} → {z_matrix.shape}")

            elif (z_raw.shape[0] == expected_shape[0] - 1 and z_raw.shape[1] == expected_shape[1] - 1):
                # Special case: Z matrix is exactly (n-1, m-1) - coordinate header issue
                print(f"✓ Detected coordinate header issue: Z matrix is (n-1, m-1)")
                print(f"  This suggests coordinate headers were incorrectly excluded from Z matrix")
                print(f"  Attempting to pad Z matrix to correct dimensions...")

                # Try to pad the matrix to the correct size
                # This is a heuristic fix for the coordinate header extraction issue
                z_matrix = np.full(expected_shape, np.nan)
                z_matrix[:z_raw.shape[0], :z_raw.shape[1]] = z_raw
                print(f"✓ Padded Z matrix from {z_raw.shape} to {z_matrix.shape}")

            elif x_type == 'column' and y_type == 'row':
                # CRITICAL FIX for X from column, Y from row case
                print(f"✓ Applying CORRECTED logic for 'X from column, Y from row' case")
                print(f"  Problem identified: Coordinate extraction is fundamentally wrong!")
                print(f"  Current Z shape: {z_raw.shape}")
                print(f"  Expected: {expected_shape}")

                # CORRECTED UNDERSTANDING:
                # When X is "from column" and Y is "from row", the data structure is:
                # - Row 0 contains X coordinates (column headers)
                # - Column 0 contains Y coordinates (row headers)
                # - The Z matrix should be the remaining data

                # The current extraction is backwards - fix it:
                print(f"  → Detected coordinate interpretation error")
                print(f"  → X should be from row {y_index} (128 values)")
                print(f"  → Y should be from column {x_index} (512 values)")
                print(f"  → Z matrix should be ({len(y_clean)}, {len(x_clean)}) = {expected_shape}")

                # CORRECTED APPROACH: Use the Z matrix as-is but transpose it
                # The Z matrix (511, 127) is missing the coordinate headers
                # But it should be transposed to match the corrected coordinate interpretation
                if z_raw.shape == (expected_shape[1] - 1, expected_shape[0] - 1):
                    # Z matrix is (X_length-1, Y_length-1) - transpose and pad
                    z_matrix = z_raw.T  # Transpose to (Y_length-1, X_length-1)
                    print(f"  → Applied transpose: {z_raw.shape} → {z_matrix.shape}")

                    # Now pad to correct dimensions
                    z_padded = np.full(expected_shape, np.nan)
                    z_padded[:z_matrix.shape[0], :z_matrix.shape[1]] = z_matrix
                    z_matrix = z_padded
                    print(f"  → Padded to correct dimensions: {z_matrix.shape}")
                else:
                    # Try transpose first
                    z_matrix = z_raw.T
                    print(f"  → Applied transpose: {z_raw.shape} → {z_matrix.shape}")

                    if z_matrix.shape != expected_shape:
                        print(f"  → Transpose didn't fix it, using raw matrix")

            else:
                # Cannot fix automatically - this indicates a data structure problem
                print(f"✗ Cannot fix dimension mismatch automatically!")
                print(f"  Raw Z shape: {z_raw.shape}")
                print(f"  Expected: {expected_shape}")
                print(f"  Total elements: {z_raw.size} vs expected {expected_shape[0] * expected_shape[1]}")

                # SPECIAL CASE: Check if this is the specific (511,127) vs (128,512) issue
                if (z_raw.shape == (511, 127) and expected_shape == (128, 512)) or \
                   (z_raw.shape[0] == expected_shape[1] - 1 and z_raw.shape[1] == expected_shape[0] - 1):
                    print(f"  → Detected specific coordinate extraction issue!")
                    print(f"  → Z matrix appears to be missing coordinate headers")
                    print(f"  → Attempting coordinate-aware correction...")

                    # This suggests the coordinate extraction included headers that should be data
                    # Try to use the Z matrix as-is but with transposed expectations
                    if z_raw.shape[1] == expected_shape[0] - 1 and z_raw.shape[0] == expected_shape[1] - 1:
                        # The Z matrix might be in the right orientation but missing headers
                        z_matrix = z_raw.T  # Try transpose first
                        print(f"  → Applied transpose: {z_raw.shape} → {z_matrix.shape}")
                    else:
                        z_matrix = z_raw
                        print(f"  → Using raw matrix for PlottingApp3D to handle")
                else:
                    # Try to salvage what we can
                    if z_raw.size > 0:
                        # Use the raw matrix and let PlottingApp3D handle the transpose
                        z_matrix = z_raw
                        print(f"  Using raw matrix {z_raw.shape} - PlottingApp3D will attempt transpose")
                    else:
                        # Create a minimal valid matrix
                        z_matrix = np.zeros(expected_shape)
                        print(f"  Created zero matrix with expected shape {expected_shape}")

            print(f"Debug: Final Z matrix shape: {z_matrix.shape}")

            # Final verification
            if z_matrix.shape == expected_shape:
                print("✓ Z matrix dimensions are correctly aligned!")
            else:
                print(f"⚠ Z matrix dimensions still don't match expected shape:")
                print(f"  Final: {z_matrix.shape}")
                print(f"  Expected: {expected_shape}")
                print(f"  PlottingApp3D will attempt to fix this with transpose")

            # Create a DataFrame with the array structure preserved
            result_data = {
                'x_coordinates': [x_clean],  # Store as array
                'y_coordinates': [y_clean],  # Store as array
                'z_matrix': [z_matrix],      # Store as 2D matrix with correct dimensions
                'data_format': ['array'],    # Mark as array format
                'x_name': [x_name],
                'y_name': [y_name],
                'z_name': [z_name]
            }

            dataset_df = pd.DataFrame(result_data)

            # Return single DataFrame - this preserves the 3D array structure with correct dimensions
            return dataset_df

    def proceed_to_plotting(self):
        """Process data and proceed directly to plotting with improved flow."""
        try:
            # Show processing message
            self.update_status("Processing data...")
            self.root.update()

            # Process data based on assignments - now returns multiple datasets for 3D
            datasets, dimension = self.process_data_for_plotting()

            if datasets is None or dimension is None:
                return  # Error already shown in process_data_for_plotting

            # Store processed data
            if isinstance(datasets, list):
                self.processed_data = datasets  # Multiple datasets (3D)
            else:
                self.processed_data = datasets  # Single dataset (2D)

            # Add small delay to prevent system overload
            self.root.after(100, lambda: self._continue_plotting_process(datasets, dimension))



        except Exception as e:
            messagebox.showerror("Error", f"Failed to proceed to plotting: {str(e)}")

    def update_status(self, message):
        """Update status message (placeholder for status updates)."""
        # This could be connected to a status bar if one exists
        print(f"Status: {message}")

    def _continue_plotting_process(self, datasets, dimension):
        """Continue the plotting process after initial data processing."""
        try:
            self.update_status("Creating plot configuration...")
            self.root.update()

            # Add another small delay before config creation
            self.root.after(100, lambda: self._create_plot_configs(datasets, dimension))

        except Exception as e:
            messagebox.showerror("Error", f"Failed to continue plotting process: {str(e)}")

    def _create_plot_configs(self, datasets, dimension):
        """Prepare basic config and launch plotting window.
        Let plotting apps create their own detailed configs.
        """
        try:
            # Create minimal config with import settings and dataset info
            # Plotting apps will create their own plot configs
            basic_config = {
                "title": f"Analysis - {Path(self.current_files[0]).stem}" if self.current_files else "Analysis",
                "dimension": dimension,
                "import_settings": {
                    "delimiter": self.delimiter_var.get(),
                    "decimal_separator": self.decimal_var.get(),
                    "has_header": self.has_header_var.get(),
                    "header_line": self.header_var.get(),
                    "data_start": self.data_start_var.get()
                }
            }

            # Add dataset information
            if isinstance(datasets, list):
                # For 3D data: Always multi_dataset=True when we have a list (even single file can have multiple datasets)
                # For 2D data: multi_dataset=True only when multiple datasets
                if dimension == 3:
                    # 3D data: Each DataFrame in list = one complete 3D dataset (one file)
                    basic_config["multi_dataset"] = True
                    basic_config["total_datasets"] = len(datasets)
                    dataset_names = []
                    for i, dataset in enumerate(datasets):
                        name = dataset.name if hasattr(dataset, 'name') else f"Dataset_{i+1}"
                        dataset_names.append(name)
                    basic_config["dataset_names"] = dataset_names
                elif len(datasets) > 1:
                    # 2D data: multi_dataset only if multiple datasets
                    basic_config["multi_dataset"] = True
                    basic_config["total_datasets"] = len(datasets)
                    dataset_names = []
                    for i, dataset in enumerate(datasets):
                        name = dataset.name if hasattr(dataset, 'name') else f"Dataset_{i+1}"
                        dataset_names.append(name)
                    basic_config["dataset_names"] = dataset_names
                else:
                    basic_config["multi_dataset"] = False
            else:
                basic_config["multi_dataset"] = False

            # Add final delay before launching plotting window
            self.root.after(200, lambda: self._launch_plotting_window(datasets, basic_config, dimension))

        except Exception as e:
            messagebox.showerror("Error", f"Failed to prepare for plotting: {str(e)}")

    def _launch_plotting_window(self, datasets, master_config, dimension):
        """Launch the plotting window as the final step."""
        try:
            self.update_status("Launching plotting window...")
            self.root.update()

            # Launch plotting window directly
            if self.on_complete_callback:
                # Trigger callback with processed data
                # Always pass the datasets (list or single) as data parameter
                self.on_complete_callback(datasets, master_config, self.current_file)

                # Close import wizard if not standalone with delay
                if not self.is_standalone:
                    self.root.after(500, self.root.destroy)
            else:
                # Standalone mode - launch plotting window directly
                # Pass datasets directly as data parameter
                self.launch_standalone_plotting(datasets, master_config, dimension)

        except Exception as e:
            messagebox.showerror("Error", f"Failed to launch plotting window: {str(e)}")

    def launch_standalone_plotting(self, data, config, dimension):
        """Launch plotting window in standalone mode."""
        try:
            from PlottingIntegration import PlottingLauncher

            # Launch appropriate plotting window
            plotting_app = PlottingLauncher.launch_plotting_window(
                data=data,
                dimension=dimension,
                title=f"Data Analysis - {Path(self.current_file).stem}",
                config=config,
                filename=self.current_file,
                project_callback=None
            )

            # Close import wizard
            self.root.destroy()

        except Exception as e:
            messagebox.showerror("Error", f"Failed to launch plotting window: {str(e)}")

    def show_post_export_dialog(self):
        """Show dialog with options to open Illustrator or Editor after export."""
        if not self.last_exported_files:
            return

        # If we have a callback, trigger it instead of showing dialog
        if self.on_complete_callback:
            self.trigger_callback()
            return

        # Create dialog window
        dialog = tk.Toplevel(self.root)
        dialog.title("What's Next?")
        dialog.geometry("400x250")
        dialog.resizable(False, False)

        # Center the dialog
        dialog.transient(self.root)
        dialog.grab_set()

        # Main frame
        main_frame = ttk.Frame(dialog, padding=20)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Title
        title_label = ttk.Label(main_frame, text="Data Export Complete!",
                               font=('Arial', 12, 'bold'))
        title_label.pack(pady=(0, 10))

        # Description
        desc_label = ttk.Label(main_frame,
                              text="Choose what you'd like to do with your exported data:",
                              font=('Arial', 10))
        desc_label.pack(pady=(0, 20))

        # Buttons frame
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X, pady=10)

        # Integrated Plotting button (new)
        if self.on_complete_callback:
            integrated_btn = ttk.Button(buttons_frame, text="🚀 Integrated Analysis",
                                       command=lambda: self.open_integrated_analysis(dialog),
                                       width=20)
            integrated_btn.pack(pady=(0, 10))

        # Illustrator button
        illustrator_btn = ttk.Button(buttons_frame, text="📊 Illustrator",
                                   command=lambda: self.open_illustrator(dialog),
                                   width=15)
        illustrator_btn.pack(side=tk.LEFT, padx=(0, 10))

        # Editor button
        editor_btn = ttk.Button(buttons_frame, text="✏️ Editor",
                              command=lambda: self.open_editor(dialog),
                              width=15)
        editor_btn.pack(side=tk.LEFT, padx=(10, 0))

        # Close button
        close_btn = ttk.Button(main_frame, text="Close",
                             command=dialog.destroy)
        close_btn.pack(pady=(20, 0))

    def open_illustrator(self, dialog):
        """Open the plotting/visualization application."""
        dialog.destroy()

        try:
            # Create and show the illustrator window
            illustrator_window = DataIllustratorWindow(self.root, self.last_exported_files, self.last_export_directory)
            illustrator_window.show()
        except Exception as e:
            messagebox.showerror("Error", f"Failed to open Illustrator: {str(e)}")

    def open_editor(self, dialog):
        """Open the data processing/editing application."""
        dialog.destroy()

        try:
            # Create and show the editor window
            editor_window = DataEditorWindow(self.root, self.last_exported_files, self.last_export_directory)
            editor_window.show()
        except Exception as e:
            messagebox.showerror("Error", f"Failed to open Editor: {str(e)}")

    def open_integrated_analysis(self, dialog):
        """Open integrated analysis in main application."""
        dialog.destroy()
        self.trigger_callback()

    def trigger_callback(self):
        """Trigger the completion callback with parsed data and plot config."""
        if self.on_complete_callback and self.parsed_data is not None:
            try:
                # Detect dimensionality from current data assignments
                dimension = self.detect_data_dimensionality()

                # Create plot_config.json with dimensionality and import settings
                plot_config = self.create_plot_config(dimension)

                # Create enhanced config for callback
                config = {
                    "title": f"Analysis - {Path(self.current_file).stem}",
                    "plot_config": plot_config,
                    "dimension": dimension,
                    "import_settings": {
                        "delimiter": self.delimiter_var.get(),
                        "decimal_separator": self.decimal_var.get(),
                        "has_header": self.has_header_var.get(),
                        "header_line": self.header_var.get(),
                        "data_start": self.data_start_var.get()
                    }
                }

                # Trigger callback with parsed data directly
                self.on_complete_callback(self.parsed_data, config, self.current_file)

                # Close import wizard if not standalone
                if not self.is_standalone:
                    self.root.destroy()

            except Exception as e:
                messagebox.showerror("Error", f"Failed to trigger callback: {str(e)}")

















    def scale_data(self):
        """Scale numeric data by a factor"""
        if self.parsed_data is None:
            messagebox.showwarning("Warning", "No data loaded")
            return

        try:
            scale_factor = tk.simpledialog.askfloat("Scale Data", "Enter scale factor:",
                                                  initialvalue=1.0)
            if scale_factor is None:
                return

            numeric_cols = self.parsed_data.select_dtypes(include=[np.number]).columns
            if len(numeric_cols) == 0:
                messagebox.showwarning("Warning", "No numeric columns to scale")
                return

            self.add_to_processing_history()

            for col in numeric_cols:
                self.parsed_data[col] = self.parsed_data[col] * scale_factor

            self.update_processing_preview()
            messagebox.showinfo("Success", f"Data scaled by factor {scale_factor}")

        except Exception as e:
            messagebox.showerror("Error", f"Scaling failed: {str(e)}")

    def interpolate_missing(self):
        """Interpolate missing values"""
        if self.parsed_data is None:
            messagebox.showwarning("Warning", "No data loaded")
            return

        try:
            numeric_cols = self.parsed_data.select_dtypes(include=[np.number]).columns
            if len(numeric_cols) == 0:
                messagebox.showwarning("Warning", "No numeric columns found")
                return

            missing_count = self.parsed_data[numeric_cols].isnull().sum().sum()
            if missing_count == 0:
                messagebox.showinfo("Info", "No missing values found")
                return

            self.add_to_processing_history()

            for col in numeric_cols:
                self.parsed_data[col] = self.parsed_data[col].interpolate()

            self.update_processing_preview()
            messagebox.showinfo("Success", f"Interpolated {missing_count} missing values")

        except Exception as e:
            messagebox.showerror("Error", f"Interpolation failed: {str(e)}")

    def remove_duplicates(self):
        """Remove duplicate rows"""
        if self.parsed_data is None:
            messagebox.showwarning("Warning", "No data loaded")
            return

        try:
            self.add_to_processing_history()
            initial_rows = len(self.parsed_data)

            self.parsed_data = self.parsed_data.drop_duplicates()
            self.parsed_data.reset_index(drop=True, inplace=True)

            removed_rows = initial_rows - len(self.parsed_data)
            self.update_processing_preview()

            messagebox.showinfo("Success", f"Removed {removed_rows} duplicate rows")

        except Exception as e:
            messagebox.showerror("Error", f"Duplicate removal failed: {str(e)}")

    def sort_data(self):
        """Sort data by selected column"""
        if self.parsed_data is None:
            messagebox.showwarning("Warning", "No data loaded")
            return

        try:
            # Get column to sort by
            columns = list(self.parsed_data.columns)
            if not columns:
                messagebox.showwarning("Warning", "No columns available")
                return

            # Simple dialog to select column
            sort_col = tk.simpledialog.askstring("Sort Data",
                f"Enter column name to sort by:\nAvailable: {', '.join(columns)}")
            if not sort_col or sort_col not in columns:
                return

            self.add_to_processing_history()

            self.parsed_data = self.parsed_data.sort_values(by=sort_col)
            self.parsed_data.reset_index(drop=True, inplace=True)

            self.update_processing_preview()
            messagebox.showinfo("Success", f"Data sorted by {sort_col}")

        except Exception as e:
            messagebox.showerror("Error", f"Sorting failed: {str(e)}")

    def transpose_data(self):
        """Transpose data (for 2D/3D data)"""
        if self.parsed_data is None:
            messagebox.showwarning("Warning", "No data loaded")
            return

        try:
            if self.data_dimension == '1D':
                messagebox.showwarning("Warning", "Cannot transpose 1D data")
                return

            self.add_to_processing_history()

            # For numeric data only
            numeric_data = self.parsed_data.select_dtypes(include=[np.number])
            if numeric_data.empty:
                messagebox.showwarning("Warning", "No numeric data to transpose")
                return

            transposed = numeric_data.T
            transposed.reset_index(inplace=True)

            self.parsed_data = transposed
            self.update_processing_preview()

            messagebox.showinfo("Success", "Data transposed successfully")

        except Exception as e:
            messagebox.showerror("Error", f"Transpose failed: {str(e)}")

    def collect_column_assignments(self):
        """Collect all column assignments from the GUI."""
        if not self.assignment_widgets:
            return {}

        data_type = self.data_type_var.get()
        assignments = {'data_type': data_type}

        if data_type == '1D':
            assignments['subtype'] = self.assignment_widgets['subtype'].get()

            if assignments['subtype'] == 'list':
                assignments['source_type'] = self.assignment_widgets['source_type'].get()
                assignments['source_index'] = int(self.assignment_widgets['source_index'].get())
            else:  # table
                selected_y = [col for col, var in self.assignment_widgets['y_columns'].items() if var.get()]
                assignments['y_columns'] = selected_y
                x_col = self.assignment_widgets['x_column'].get()
                assignments['x_column'] = x_col if x_col != 'None' else None

            # Include custom column names for 1D
            if 'x_name' in self.assignment_widgets:
                assignments['x_name'] = self.assignment_widgets['x_name'].get()

        elif data_type == '2D':
            assignments['multiple_measurements'] = self.assignment_widgets['multiple_measurements'].get()
            assignments['x_column'] = self.assignment_widgets['x_column'].get()
            selected_y = [col for col, var in self.assignment_widgets['y_columns'].items() if var.get()]
            assignments['y_columns'] = selected_y

            # Include custom column names for 2D
            if 'x_name' in self.assignment_widgets:
                assignments['x_name'] = self.assignment_widgets['x_name'].get()
            if 'y_name' in self.assignment_widgets:
                assignments['y_name'] = self.assignment_widgets['y_name'].get()

        elif data_type == '3D':
            assignments['x_type'] = self.assignment_widgets['x_type'].get()
            assignments['y_type'] = self.assignment_widgets['y_type'].get()
            # For 3D data, X and Y are always from first row/column (index 0)
            assignments['x_index'] = 0
            assignments['y_index'] = 0
            # Include skip header setting (applies to both X and Y)
            skip_first_cell = self.assignment_widgets.get('skip_first_cell', tk.BooleanVar(value=True)).get()
            assignments['x_skip_header'] = skip_first_cell
            assignments['y_skip_header'] = skip_first_cell
            # Include custom column names for 3D
            if 'x_name' in self.assignment_widgets:
                assignments['x_name'] = self.assignment_widgets['x_name'].get()
            if 'y_name' in self.assignment_widgets:
                assignments['y_name'] = self.assignment_widgets['y_name'].get()
            if 'z_name' in self.assignment_widgets:
                assignments['z_name'] = self.assignment_widgets['z_name'].get()

        return assignments

    def get_excluded_rows(self):
        """Get list of excluded row indices."""
        # For now, return empty list - can be extended later
        return []

    def get_excluded_cols(self):
        """Get list of excluded column indices."""
        # For now, return empty list - can be extended later
        return []

    def get_header_name_for_column(self, column_index):
        """Get the header name for a column index, fallback to column index if no header."""
        if self.parsed_data is not None and column_index < len(self.parsed_data.columns):
            column_name = str(self.parsed_data.columns[column_index])
            # Check if this looks like a real header (not just a default column name)
            if not column_name.startswith('Unnamed:') and column_name != str(column_index):
                # Clean the header name for use in filename
                return column_name.replace(' ', '_').replace('/', '_').replace('\\', '_').replace(':', '_')

        # Fallback to column index
        return f"col{column_index}"

    def get_header_name_for_column_name(self, column_name):
        """Get a clean header name from a column name, fallback to the column name itself."""
        if column_name and not str(column_name).startswith('Unnamed:'):
            # Clean the header name for use in filename
            return str(column_name).replace(' ', '_').replace('/', '_').replace('\\', '_').replace(':', '_')

        # Fallback to the original column name
        return str(column_name).replace(' ', '_').replace('/', '_').replace('\\', '_').replace(':', '_')

    def select_project_dir(self):
        """Select project directory for export."""
        directory = filedialog.askdirectory(title="Select Project Directory")
        if directory:
            self.project_dir = directory
            # Note: project_label no longer exists since export tab was removed

    def update_export_summary(self):
        """Update the export summary - method kept for compatibility but no longer used."""
        # This method is no longer needed since we removed the export tab
        # Keeping it to prevent AttributeError if called elsewhere
        pass

    def export_data(self):
        """Export organized data based on assignments."""
        if self.parsed_data is None or self.parsed_data.empty:
            messagebox.showerror("Error", "No data to export")
            return

        try:
            # Collect assignments
            assignments = self.collect_column_assignments()
            data_type = assignments.get('data_type')

            if not data_type:
                messagebox.showerror("Error", "Please specify data type and assignments")
                return

            # Get base output directory
            if hasattr(self, 'project_dir') and self.project_dir:
                base_output_dir = self.project_dir
            else:
                messagebox.showerror("Error", "Please select a project directory first")
                return

            # Create new folder if requested
            if self.create_folder_var.get() and self.folder_name_var.get().strip():
                folder_name = self.folder_name_var.get().strip()
                output_dir = os.path.join(base_output_dir, folder_name)
                os.makedirs(output_dir, exist_ok=True)
                # Create metadata folder inside the new folder
                metadata_dir = os.path.join(output_dir, folder_name + '_metadata')
            else:
                output_dir = base_output_dir
                # Create metadata folder inside the base directory
                base_name = Path(base_output_dir).name
                metadata_dir = os.path.join(output_dir, base_name + '_metadata')

            os.makedirs(metadata_dir, exist_ok=True)

            # Get base filename
            filename = Path(self.current_file).stem if self.current_file else "data"

            exported_files = []
            datasets_created = 0

            if len(self.current_files) == 1:
                # Single file export
                if data_type == '1D':
                    exported_files, datasets_created = self._export_1d_data(assignments, output_dir, filename)
                elif data_type == '2D':
                    exported_files, datasets_created = self._export_2d_data(assignments, output_dir, filename)
                elif data_type == '3D':
                    exported_files, datasets_created = self._export_3d_data(assignments, output_dir, filename)
            else:
                # Multi-file export
                if data_type == '1D':
                    exported_files, datasets_created = self._export_multi_file_1d_data(assignments, output_dir)
                elif data_type == '2D':
                    exported_files, datasets_created = self._export_multi_file_2d_data(assignments, output_dir)
                elif data_type == '3D':
                    exported_files, datasets_created = self._export_multi_file_3d_data(assignments, output_dir)

            # Save file info if requested (save to metadata folder)
            if self.file_analyses and self.save_file_info_var.get():
                try:
                    for file_path, analysis in self.file_analyses.items():
                        base_name = Path(file_path).stem
                        saved_info_files = self.header_detector.save_headers_metadata(
                            file_path,
                            analysis,
                            metadata_dir,  # Save to metadata folder instead of output_dir
                            save_content=True
                        )
                        # Don't add metadata files to exported_files list to avoid confusion
                        self.summary_text.insert(tk.END, f"Saved metadata to: {metadata_dir}\n")
                except Exception as e:
                    messagebox.showwarning("Warning", f"Data exported successfully, but failed to save file info: {str(e)}")

            # Show success message
            file_list = '\n'.join([f"  - {Path(f).name}" for f in exported_files])
            success_msg = f"Successfully exported {datasets_created} dataset(s)"

            if self.file_analyses and self.save_file_info_var.get():
                success_msg += f" and file information"

            success_msg += f":\n\n{file_list}"
            messagebox.showinfo("Export Complete", success_msg)

            # Store export information for post-export options
            self.last_exported_files = exported_files
            self.last_export_directory = output_dir

            # Update summary
            self.summary_text.insert(tk.END, f"\n\nExport completed successfully!\n")
            self.summary_text.insert(tk.END, f"Location: {output_dir}\n")
            self.summary_text.insert(tk.END, f"Files created: {len(exported_files)}\n")
            self.summary_text.insert(tk.END, f"Datasets: {datasets_created}\n")

            if self.file_analyses and self.save_file_info_var.get():
                self.summary_text.insert(tk.END, f"File information saved: Yes\n")

            # Show post-export options dialog
            self.show_post_export_dialog()

        except Exception as e:
            messagebox.showerror("Error", f"Export failed: {str(e)}")

    def _export_multi_file_3d_data(self, assignments, output_dir):
        """Export multiple 3D data files with same assignments."""
        exported_files = []
        datasets_created = 0

        # Get parsing parameters
        delimiter = self.delimiter_var.get().replace('\\t', '\t')
        decimal_sep = self.decimal_var.get()

        header_line = int(self.header_var.get()) if self.has_header_var.get() else -1
        data_start = int(self.data_start_var.get())

        # Get exclusions
        excluded_rows = self.get_excluded_rows()
        excluded_cols = self.get_excluded_cols()

        progress_window = tk.Toplevel(self.root)
        progress_window.title("Exporting Files")
        progress_window.geometry("400x150")
        progress_window.transient(self.root)
        progress_window.grab_set()

        progress_label = ttk.Label(progress_window, text="Preparing export...")
        progress_label.pack(pady=20)

        progress_bar = ttk.Progressbar(progress_window, length=300, mode='determinate')
        progress_bar.pack(pady=10)
        progress_bar['maximum'] = len(self.current_files)

        cancel_button = ttk.Button(progress_window, text="Cancel",
                                  command=lambda: setattr(self, '_cancel_export', True))
        cancel_button.pack(pady=10)

        self._cancel_export = False

        try:
            for i, file_path in enumerate(self.current_files):
                if self._cancel_export:
                    break

                file_name = Path(file_path).stem
                progress_label.config(text=f"Processing {file_name}...")
                progress_window.update()

                # Parse this file with same parameters
                file_analysis = self.file_analyses.get(file_path)
                parsed_data = self.parser.parse_file(
                    file_path, delimiter, decimal_sep, header_line, data_start,
                    excluded_rows, excluded_cols, file_analysis
                )

                if parsed_data.empty:
                    continue

                # Temporarily set parsed_data for export
                original_data = self.parsed_data
                self.parsed_data = parsed_data

                try:
                    # Export this file's data
                    file_exported, file_datasets = self._export_3d_data(assignments, output_dir, file_name)
                    exported_files.extend(file_exported)
                    datasets_created += file_datasets
                finally:
                    # Restore original data
                    self.parsed_data = original_data

                progress_bar['value'] = i + 1
                progress_window.update()

        finally:
            progress_window.destroy()

        if self._cancel_export:
            messagebox.showinfo("Export Cancelled", f"Export was cancelled. {datasets_created} files were processed.")

        return exported_files, datasets_created

    def _export_1d_data(self, assignments, output_dir, filename):
        """Export 1D data based on assignments."""
        exported_files = []
        datasets_created = 0

        subtype = assignments.get('subtype', 'list')

        if subtype == 'list':
            # Single list from column or row
            source_type = assignments.get('source_type', 'column')
            source_index = assignments.get('source_index', 0)

            if source_type == 'column':
                data = self.parsed_data.iloc[:, source_index]
                header_name = self.get_header_name_for_column(source_index)
                export_filename = f"{filename}_{header_name}_1D.csv"
            else:  # row
                data = self.parsed_data.iloc[source_index, :]
                export_filename = f"{filename}_row{source_index}_1D.csv"

            # Create DataFrame with custom column names
            x_name = assignments.get('x_name', 'Index')
            y_name = assignments.get('y_name', 'Value')  # For 1D, Y represents the data values

            export_df = pd.DataFrame({
                x_name: range(len(data)),
                y_name: data.values
            })

            export_path = Path(output_dir) / export_filename
            export_df.to_csv(export_path, index=False)
            exported_files.append(export_filename)
            datasets_created = 1

        elif subtype == 'table':
            # Multiple datasets from table
            selection_method = assignments.get('selection_method', 'include')
            column_selection = assignments.get('column_selection', {})
            row_selection = assignments.get('row_selection', {})
            x_source = assignments.get('x_source', 'none')
            x_index = assignments.get('x_index', 0)

            # Determine selected columns and rows
            if selection_method == 'include':
                selected_cols = [i for i, selected in column_selection.items() if selected]
                selected_rows = [i for i, selected in row_selection.items() if selected]
            else:  # exclude
                all_cols = list(range(len(self.parsed_data.columns)))
                all_rows = list(range(len(self.parsed_data)))
                selected_cols = [i for i in all_cols if not column_selection.get(i, False)]
                selected_rows = [i for i in all_rows if not row_selection.get(i, False)]

            # Get X data if specified
            x_data = None
            if x_source == 'column' and x_index < len(self.parsed_data.columns):
                x_data = self.parsed_data.iloc[selected_rows, x_index]
            elif x_source == 'row' and x_index < len(self.parsed_data):
                x_data = self.parsed_data.iloc[x_index, selected_cols]

            # Export data
            if selected_cols and selected_rows:
                data_subset = self.parsed_data.iloc[selected_rows, :]
                data_subset = data_subset.iloc[:, selected_cols]

                # Create separate files for each column (dataset)
                for col_idx, col_pos in enumerate(selected_cols):
                    col_name = self.parsed_data.columns[col_pos]
                    header_name = self.get_header_name_for_column_name(col_name)
                    y_data = data_subset.iloc[:, col_idx]

                    # Get custom column names
                    x_name = assignments.get('x_name', 'X')
                    y_name = assignments.get('y_name', 'Y')

                    if x_data is not None and x_source == 'column':
                        export_df = pd.DataFrame({
                            x_name: x_data.values,
                            y_name: y_data.values
                        })
                    elif x_data is not None and x_source == 'row':
                        # For row-based X, use the same X value for all Y points
                        x_val = x_data.iloc[col_idx] if col_idx < len(x_data) else x_data.iloc[0]
                        export_df = pd.DataFrame({
                            x_name: [x_val] * len(y_data),
                            y_name: y_data.values
                        })
                    else:
                        export_df = pd.DataFrame({
                            x_name: range(len(y_data)),
                            y_name: y_data.values
                        })

                    export_filename = f"{filename}_{header_name}_1D.csv"
                    export_path = Path(output_dir) / export_filename
                    export_df.to_csv(export_path, index=False)
                    exported_files.append(export_filename)
                    datasets_created += 1

        return exported_files, datasets_created

    def _export_2d_data(self, assignments, output_dir, filename):
        """Export 2D data based on assignments."""
        exported_files = []
        datasets_created = 0

        x_column = assignments.get('x_column', '')
        y_columns = assignments.get('y_columns', [])
        multiple_measurements = assignments.get('multiple_measurements', False)

        if not x_column or not y_columns:
            raise ValueError("X column and at least one Y column must be specified for 2D data")

        # Get X data
        if x_column not in self.parsed_data.columns:
            raise ValueError(f"X column '{x_column}' not found in data")

        x_data = self.parsed_data[x_column]

        if multiple_measurements:
            # Create separate files for each Y column
            for y_col in y_columns:
                if y_col not in self.parsed_data.columns:
                    continue

                y_data = self.parsed_data[y_col]

                # Remove rows with NaN values
                valid_mask = ~(pd.isna(x_data) | pd.isna(y_data))
                clean_x = x_data[valid_mask]
                clean_y = y_data[valid_mask]

                # Get custom column names
                x_name = assignments.get('x_name', 'X')
                y_name = assignments.get('y_name', 'Y')

                export_df = pd.DataFrame({
                    x_name: clean_x.values,
                    y_name: clean_y.values
                })

                # Get header name for filename
                header_name = self.get_header_name_for_column_name(y_col)
                export_filename = f"{filename}_{header_name}_2D.csv"
                export_path = Path(output_dir) / export_filename
                export_df.to_csv(export_path, index=False)
                exported_files.append(export_filename)
                datasets_created += 1
        else:
            # Single file with all Y columns
            x_name = assignments.get('x_name', 'X')
            y_name = assignments.get('y_name', 'Y')

            export_data = {x_name: x_data}

            # Add all Y columns with custom naming
            for y_col in y_columns:
                if y_col in self.parsed_data.columns:
                    if len(y_columns) == 1:
                        # Single Y column - use the custom Y name
                        export_data[y_name] = self.parsed_data[y_col]
                    else:
                        # Multiple Y columns - use custom Y name with suffix
                        export_data[f'{y_name}_{y_col}'] = self.parsed_data[y_col]

            export_df = pd.DataFrame(export_data)

            # Remove rows with NaN in X column
            export_df = export_df.dropna(subset=[x_name])

            # Create filename based on Y columns
            if len(y_columns) == 1:
                header_name = self.get_header_name_for_column_name(y_columns[0])
                export_filename = f"{filename}_{header_name}_2D.csv"
            else:
                export_filename = f"{filename}_combined_2D.csv"
            export_path = Path(output_dir) / export_filename
            export_df.to_csv(export_path, index=False)
            exported_files.append(export_filename)
            datasets_created = 1

        return exported_files, datasets_created

    def _export_3d_data(self, assignments, output_dir, filename):
        """Export 3D data as a flattened table that includes all data regardless of size differences."""
        exported_files = []
        datasets_created = 0

        x_type = assignments.get('x_type', 'column')
        y_type = assignments.get('y_type', 'column')
        x_index = assignments.get('x_index', 0)
        y_index = assignments.get('y_index', 0)  # For 3D, both are always 0
        x_skip_header = assignments.get('x_skip_header', True)
        y_skip_header = assignments.get('x_skip_header', True)

        # Get X data
        if x_type == 'column':
            if x_index >= len(self.parsed_data.columns):
                raise ValueError(f"X column index {x_index} out of range")
            x_data = self.parsed_data.iloc[:, x_index]
            # Skip first cell if header skip is enabled
            if x_skip_header and len(x_data) > 1:
                x_data = x_data.iloc[1:]
            x_source_label = f"Column_{x_index}"
        else:  # row
            if x_index >= len(self.parsed_data):
                raise ValueError(f"X row index {x_index} out of range")
            x_data = self.parsed_data.iloc[x_index, :]
            # Skip first cell if header skip is enabled
            if x_skip_header and len(x_data) > 1:
                x_data = x_data.iloc[1:]
            x_source_label = f"Row_{x_index}"

        # Get Y data
        if y_type == 'column':
            if y_index >= len(self.parsed_data.columns):
                raise ValueError(f"Y column index {y_index} out of range")
            y_data = self.parsed_data.iloc[:, y_index]
            # Skip first cell if header skip is enabled
            if y_skip_header and len(y_data) > 1:
                y_data = y_data.iloc[1:]
            y_source_label = f"Column_{y_index}"
        else:  # row
            if y_index >= len(self.parsed_data):
                raise ValueError(f"Y row index {y_index} out of range")
            y_data = self.parsed_data.iloc[y_index, :]
            # Skip first cell if header skip is enabled
            if y_skip_header and len(y_data) > 1:
                y_data = y_data.iloc[1:]
            y_source_label = f"Row_{y_index}"

        # Determine Z data (all remaining columns/rows after X and Y assignment)
        if x_type == 'column' and y_type == 'column':
            # Both X and Y are columns, Z is remaining columns
            used_cols = {x_index, y_index}
            z_cols = [i for i in range(len(self.parsed_data.columns)) if i not in used_cols]
            z_data = self.parsed_data.iloc[:, z_cols] if z_cols else None
            z_col_names = [self.parsed_data.columns[i] for i in z_cols] if z_cols else []
            # Skip first row if either X or Y skip header is enabled
            if (x_skip_header or y_skip_header) and z_data is not None and len(z_data) > 1:
                z_data = z_data.iloc[1:]

        elif x_type == 'row' and y_type == 'column':
            # X from row, Y from column, Z from remaining data
            remaining_data = self.parsed_data.drop(self.parsed_data.index[x_index]).drop(self.parsed_data.columns[y_index], axis=1)
            z_data = remaining_data
            z_col_names = list(remaining_data.columns)
            # Skip first column if Y skip header is enabled
            if y_skip_header and z_data is not None and len(z_data.columns) > 0:
                z_data = z_data.iloc[:, 1:] if len(z_data.columns) > 1 else z_data

        elif x_type == 'column' and y_type == 'row':
            # X from column, Y from row, Z from remaining data
            remaining_data = self.parsed_data.drop(self.parsed_data.columns[x_index], axis=1).drop(self.parsed_data.index[y_index])
            z_data = remaining_data
            z_col_names = list(remaining_data.columns)
            # Skip first row if X skip header is enabled
            if x_skip_header and z_data is not None and len(z_data) > 0:
                z_data = z_data.iloc[1:] if len(z_data) > 1 else z_data

        else:  # both from rows
            # Both X and Y are rows, Z is remaining rows
            used_rows = {x_index, y_index}
            z_rows = [i for i in range(len(self.parsed_data)) if i not in used_rows]
            z_data = self.parsed_data.iloc[z_rows, :] if z_rows else None
            z_col_names = list(self.parsed_data.columns)
            # Skip first column if either X or Y skip header is enabled
            if (x_skip_header or y_skip_header) and z_data is not None and len(z_data.columns) > 0:
                z_data = z_data.iloc[:, 1:] if len(z_data.columns) > 1 else z_data
                z_col_names = z_col_names[1:] if len(z_col_names) > 1 else z_col_names

        if z_data is None or z_data.empty:
            raise ValueError("No Z data available after X and Y assignment")

        # Create flattened export table that creates proper 3D coordinate grid
        flattened_rows = []

        # Get custom column names
        x_name = assignments.get('x_name', 'X')
        y_name = assignments.get('y_name', 'Y')
        z_name = assignments.get('z_name', 'Z')

        # For proper 3D flattening, we need to create a grid where each X,Y combination
        # gets paired with its corresponding Z value from the data matrix

        if x_type == 'column' and y_type == 'column':
            # Both X and Y are columns - create grid from their values
            for row_idx in range(len(z_data)):
                x_val = x_data.iloc[row_idx] if row_idx < len(x_data) else x_data.iloc[-1]
                y_val = y_data.iloc[row_idx] if row_idx < len(y_data) else y_data.iloc[-1]

                # For each Z column, create a flattened row with this X,Y pair
                z_row = z_data.iloc[row_idx]
                for z_col_idx, z_col_name in enumerate(z_col_names):
                    if z_col_idx < len(z_row):
                        z_val = z_row.iloc[z_col_idx]

                        flattened_rows.append({
                            x_name: x_val,
                            y_name: y_val,
                            z_name: z_val,
                            #'Z_Column': z_col_name,
                            #'Row_Index': row_idx,
                            #'Z_Column_Index': z_col_idx
                        })

        elif x_type == 'column' and y_type == 'row':
            # X from column, Y from row - create proper X,Y grid
            y_values = y_data.values  # Y coordinates from the specified row

            for row_idx in range(len(z_data)):
                x_val = x_data.iloc[row_idx] if row_idx < len(x_data) else x_data.iloc[-1]

                # For each Z column, pair with corresponding Y coordinate
                z_row = z_data.iloc[row_idx]
                for z_col_idx, z_col_name in enumerate(z_col_names):
                    if z_col_idx < len(z_row) and z_col_idx < len(y_values):
                        y_val = y_values[z_col_idx]  # Y coordinate for this Z column
                        z_val = z_row.iloc[z_col_idx]

                        flattened_rows.append({
                            x_name: x_val,
                            y_name: y_val,
                            z_name: z_val,
                            #'Z_Column': z_col_name,
                            #'Row_Index': row_idx,
                            #'Z_Column_Index': z_col_idx
                        })

        elif x_type == 'row' and y_type == 'column':
            # X from row, Y from column - create proper X,Y grid
            x_values = x_data.values  # X coordinates from the specified row

            for row_idx in range(len(z_data)):
                y_val = y_data.iloc[row_idx] if row_idx < len(y_data) else y_data.iloc[-1]

                # For each Z column, pair with corresponding X coordinate
                z_row = z_data.iloc[row_idx]
                for z_col_idx, z_col_name in enumerate(z_col_names):
                    if z_col_idx < len(z_row) and z_col_idx < len(x_values):
                        x_val = x_values[z_col_idx]  # X coordinate for this Z column
                        z_val = z_row.iloc[z_col_idx]

                        flattened_rows.append({
                            x_name: x_val,
                            y_name: y_val,
                            z_name: z_val,
                            #'Z_Column': z_col_name,
                            #'Row_Index': row_idx,
                            #'Z_Column_Index': z_col_idx
                        })

        else:  # both from rows
            # Both X and Y from rows - create grid from row values
            x_values = x_data.values  # X coordinates from specified row
            y_values = y_data.values  # Y coordinates from specified row

            # Create grid: each X with each Y, using Z data as matrix
            for row_idx in range(len(z_data)):
                for col_idx in range(len(z_data.columns)):
                    if row_idx < len(x_values) and col_idx < len(y_values):
                        x_val = x_values[row_idx]
                        y_val = y_values[col_idx]
                        z_val = z_data.iloc[row_idx, col_idx]

                        flattened_rows.append({
                            x_name: x_val,
                            y_name: y_val,
                            z_name: z_val,
                            #'Z_Column': z_data.columns[col_idx],
                            #'Row_Index': row_idx,
                            #'Z_Column_Index': col_idx
                        })

        # Create the flattened DataFrame and export
        if flattened_rows:
            export_df = pd.DataFrame(flattened_rows)

            # Create export filename
            export_filename = f"{filename}_flattened_3D.csv"
            export_path = Path(output_dir) / export_filename
            export_df.to_csv(export_path, index=False)
            exported_files.append(export_filename)
            datasets_created = 1
        else:
            raise ValueError("No data could be flattened for export")

        return exported_files, datasets_created

    def _export_multi_file_1d_data(self, assignments, output_dir):
        """Export multiple 1D data files with same assignments."""
        return self._export_multi_file_generic(assignments, output_dir, self._export_1d_data)

    def _export_multi_file_2d_data(self, assignments, output_dir):
        """Export multiple 2D data files with same assignments."""
        return self._export_multi_file_generic(assignments, output_dir, self._export_2d_data)

    def _export_multi_file_generic(self, assignments, output_dir, export_method):
        """Generic multi-file export method for any data type."""
        exported_files = []
        datasets_created = 0

        # Get parsing parameters
        delimiter = self.delimiter_var.get().replace('\\t', '\t')
        decimal_sep = self.decimal_var.get()

        header_line = int(self.header_var.get()) if self.has_header_var.get() else -1
        data_start = int(self.data_start_var.get())

        # Get exclusions
        excluded_rows = self.get_excluded_rows()
        excluded_cols = self.get_excluded_cols()

        progress_window = tk.Toplevel(self.root)
        progress_window.title("Exporting Files")
        progress_window.geometry("400x150")
        progress_window.transient(self.root)
        progress_window.grab_set()

        progress_label = ttk.Label(progress_window, text="Preparing export...")
        progress_label.pack(pady=20)

        progress_bar = ttk.Progressbar(progress_window, length=300, mode='determinate')
        progress_bar.pack(pady=10)
        progress_bar['maximum'] = len(self.current_files)

        cancel_button = ttk.Button(progress_window, text="Cancel",
                                  command=lambda: setattr(self, '_cancel_export', True))
        cancel_button.pack(pady=10)

        self._cancel_export = False

        try:
            for i, file_path in enumerate(self.current_files):
                if self._cancel_export:
                    break

                file_name = Path(file_path).stem
                progress_label.config(text=f"Processing {file_name}...")
                progress_window.update()

                # Parse this file with same parameters
                file_analysis = self.file_analyses.get(file_path)
                parsed_data = self.parser.parse_file(
                    file_path, delimiter, decimal_sep, header_line, data_start,
                    excluded_rows, excluded_cols, file_analysis
                )

                if parsed_data.empty:
                    continue

                # Temporarily set parsed_data for export
                original_data = self.parsed_data
                self.parsed_data = parsed_data

                try:
                    # Export this file's data
                    file_exported, file_datasets = export_method(assignments, output_dir, file_name)
                    exported_files.extend(file_exported)
                    datasets_created += file_datasets
                finally:
                    # Restore original data
                    self.parsed_data = original_data

                progress_bar['value'] = i + 1
                progress_window.update()

        finally:
            progress_window.destroy()

        if self._cancel_export:
            messagebox.showinfo("Export Cancelled", f"Export was cancelled. {datasets_created} files were processed.")

        return exported_files, datasets_created

    def run(self):
        """Run the application."""
        self.root.mainloop()

    def preview_1d_assignment(self, assignments):
        """Preview 1D data assignment."""
        subtype = assignments.get('subtype', 'list')

        if subtype == 'list':
            source_type = assignments.get('source_type', 'column')
            source_index = assignments.get('source_index', 0)

            if source_type == 'column':
                preview_data = self.parsed_data.iloc[:, source_index]
                self.assignment_preview_tree['columns'] = ['Index', 'Value']
                self.assignment_preview_tree['show'] = 'headings'

                for col in self.assignment_preview_tree['columns']:
                    self.assignment_preview_tree.heading(col, text=col)
                    self.assignment_preview_tree.column(col, width=100)

                for i, val in enumerate(preview_data[:20]):  # Show first 20 values
                    self.assignment_preview_tree.insert('', 'end', values=[i, val])

                self.assignment_preview_label.config(
                    text=f"Preview: 1D list from column {source_index} ({len(preview_data)} values)")

            else:  # row
                preview_data = self.parsed_data.iloc[source_index, :]
                self.assignment_preview_tree['columns'] = ['Index', 'Value']
                self.assignment_preview_tree['show'] = 'headings'

                for col in self.assignment_preview_tree['columns']:
                    self.assignment_preview_tree.heading(col, text=col)
                    self.assignment_preview_tree.column(col, width=100)

                for i, val in enumerate(preview_data[:20]):  # Show first 20 values
                    self.assignment_preview_tree.insert('', 'end', values=[i, val])

                self.assignment_preview_label.config(
                    text=f"Preview: 1D list from row {source_index} ({len(preview_data)} values)")

        else:  # table
            # Show preview of selected columns/rows
            selection_method = assignments.get('selection_method', 'include')
            column_selection = assignments.get('column_selection', {})
            row_selection = assignments.get('row_selection', {})

            # Apply column selection
            if selection_method == 'include':
                selected_cols = [i for i, selected in column_selection.items() if selected]
            else:
                selected_cols = [i for i, selected in column_selection.items() if not selected]

            # Apply row selection
            if selection_method == 'include':
                selected_rows = [i for i, selected in row_selection.items() if selected]
            else:
                selected_rows = [i for i, selected in row_selection.items() if not selected]

            if selected_cols and selected_rows:
                preview_data = self.parsed_data.iloc[selected_rows[:20], selected_cols[:10]]  # Limit preview size

                columns = ['Index'] + [f'Col_{i}' for i in selected_cols[:10]]
                self.assignment_preview_tree['columns'] = columns
                self.assignment_preview_tree['show'] = 'headings'

                for col in columns:
                    self.assignment_preview_tree.heading(col, text=col)
                    self.assignment_preview_tree.column(col, width=80)

                for idx, (_, row) in enumerate(preview_data.iterrows()):
                    values = [str(selected_rows[idx])] + [str(val) for val in row.values]
                    self.assignment_preview_tree.insert('', 'end', values=values)

                self.assignment_preview_label.config(
                    text=f"Preview: {len(selected_rows)} rows × {len(selected_cols)} columns")

    def preview_2d_assignment(self, assignments):
        """Preview 2D data assignment."""
        x_column = assignments.get('x_column', '')
        y_columns = assignments.get('y_columns', [])
        multiple_measurements = assignments.get('multiple_measurements', False)

        if x_column and y_columns:
            # Show X and selected Y columns
            columns_to_show = [x_column] + y_columns[:15]  # Limit to first 15 Y columns
            preview_data = self.parsed_data[columns_to_show].head(20)

            display_columns = ['Index'] + columns_to_show
            self.assignment_preview_tree['columns'] = display_columns
            self.assignment_preview_tree['show'] = 'headings'

            for col in display_columns:
                self.assignment_preview_tree.heading(col, text=col)
                self.assignment_preview_tree.column(col, width=100)

            for idx, (_, row) in enumerate(preview_data.iterrows()):
                values = [str(idx)] + [str(val) for val in row.values]
                self.assignment_preview_tree.insert('', 'end', values=values)

            if multiple_measurements:
                self.assignment_preview_label.config(
                    text=f"Preview: Multiple 2D datasets - X: {x_column}, Y columns: {len(y_columns)}")
            else:
                self.assignment_preview_label.config(
                    text=f"Preview: Single 2D dataset - X: {x_column}, Y columns: {len(y_columns)}")

    def preview_3d_assignment(self, assignments):
        """Preview 3D data assignment in the format: x;y;z1;z2;z3;..."""
        x_type = assignments.get('x_type', 'column')
        y_type = assignments.get('y_type', 'column')
        x_index = assignments.get('x_index', 0)
        y_index = assignments.get('y_index', 0)  # For 3D, both are always 0

        # Clear existing columns
        self.assignment_preview_tree['columns'] = []
        self.assignment_preview_tree['show'] = 'headings'

        try:
            # Get X and Y data
            if x_type == 'column':
                x_data = self.parsed_data.iloc[:, x_index] if x_index < len(self.parsed_data.columns) else None
            else:  # row
                x_data = self.parsed_data.iloc[x_index, :] if x_index < len(self.parsed_data) else None

            if y_type == 'column':
                y_data = self.parsed_data.iloc[:, y_index] if y_index < len(self.parsed_data.columns) else None
            else:  # row
                y_data = self.parsed_data.iloc[y_index, :] if y_index < len(self.parsed_data) else None

            # Determine Z data (remaining columns/rows after X and Y)
            if x_type == 'column' and y_type == 'column':
                # Both X and Y are columns, Z is remaining columns
                used_cols = {x_index, y_index}
                z_cols = [i for i in range(len(self.parsed_data.columns)) if i not in used_cols]
                z_data = self.parsed_data.iloc[:, z_cols] if z_cols else None
                z_dimension = len(z_cols)

            elif x_type == 'row' and y_type == 'column':
                # X from row, Y from column, Z from remaining data
                remaining_data = self.parsed_data.drop(self.parsed_data.index[x_index]).drop(self.parsed_data.columns[y_index], axis=1)
                z_data = remaining_data
                z_dimension = len(remaining_data.columns)

            elif x_type == 'column' and y_type == 'row':
                # X from column, Y from row, Z from remaining data
                remaining_data = self.parsed_data.drop(self.parsed_data.columns[x_index], axis=1).drop(self.parsed_data.index[y_index])
                z_data = remaining_data
                z_dimension = len(remaining_data.columns)

            else:  # both from rows
                # Both X and Y are rows, Z is remaining rows
                used_rows = {x_index, y_index}
                z_rows = [i for i in range(len(self.parsed_data)) if i not in used_rows]
                z_data = self.parsed_data.iloc[z_rows, :] if z_rows else None
                z_dimension = len(z_rows)

            # Create column headers: X, Y, Z1, Z2, Z3, ... (with dimension info)
            z_headers = [f'Z{i+1}' for i in range(min(z_dimension, 10))]  # Limit to first 10 Z columns for display
            columns = ['X', 'Y'] + z_headers
            if z_dimension > 10:
                columns.append('...')

            self.assignment_preview_tree['columns'] = columns

            # Set column headings with Z dimension info
            self.assignment_preview_tree.heading('X', text='X')
            self.assignment_preview_tree.heading('Y', text='Y')
            for i, z_header in enumerate(z_headers):
                self.assignment_preview_tree.heading(z_header, text=z_header)
            if z_dimension > 10:
                self.assignment_preview_tree.heading('...', text='...')

            # Set column widths
            for col in columns:
                self.assignment_preview_tree.column(col, width=80)

            # Populate data rows (show first 20 rows)
            if x_data is not None and y_data is not None and z_data is not None:
                preview_rows = min(20, len(x_data) if hasattr(x_data, '__len__') else 1)

                for i in range(preview_rows):
                    # Get X and Y values for this row
                    if x_type == 'column':
                        x_val = x_data.iloc[i] if i < len(x_data) else 'N/A'
                    else:  # row - X is constant for all rows
                        x_val = x_data.iloc[i] if i < len(x_data) else 'N/A'

                    if y_type == 'column':
                        y_val = y_data.iloc[i] if i < len(y_data) else 'N/A'
                    else:  # row - Y is constant for all rows
                        y_val = y_data.iloc[i] if i < len(y_data) else 'N/A'

                    # Get Z values for this row (up to 10 columns)
                    z_vals = []
                    if hasattr(z_data, 'iloc'):
                        for j in range(min(z_dimension, 10)):
                            if i < len(z_data) and j < len(z_data.columns):
                                z_vals.append(str(z_data.iloc[i, j]))
                            else:
                                z_vals.append('N/A')

                    # Create row values
                    row_values = [str(x_val), str(y_val)] + z_vals
                    if z_dimension > 10:
                        row_values.append('...')

                    self.assignment_preview_tree.insert('', 'end', values=row_values)

            # Update label with dimension information
            self.assignment_preview_label.config(
                text=f"Preview: 3D data (X: {x_type} {x_index}, Y: {y_type} {y_index}, Z: {z_dimension} dimensions) - Showing first {min(20, len(self.parsed_data))} rows"
            )

        except Exception as e:
            # Error handling
            self.assignment_preview_tree['columns'] = ['Error']
            self.assignment_preview_tree['show'] = 'headings'
            self.assignment_preview_tree.heading('Error', text='Error')
            self.assignment_preview_tree.column('Error', width=300)
            self.assignment_preview_tree.insert('', 'end', values=[f"Preview error: {str(e)}"])

            self.assignment_preview_label.config(text="Error generating 3D preview - check assignments")


class DataIllustratorWindow:
    """Window for data visualization and plotting."""

    def __init__(self, parent, exported_files, export_directory):
        self.parent = parent
        self.exported_files = exported_files
        self.export_directory = export_directory
        self.window = None

    def show(self):
        """Show the illustrator window."""
        self.window = tk.Toplevel(self.parent)
        self.window.title("Data Illustrator - Plotting & Visualization")
        self.window.geometry("1000x700")

        # Main frame
        main_frame = ttk.Frame(self.window, padding=10)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Title
        title_label = ttk.Label(main_frame, text="Data Illustrator",
                               font=('Arial', 14, 'bold'))
        title_label.pack(pady=(0, 10))

        # File selection
        file_frame = ttk.LabelFrame(main_frame, text="Select Data File", padding=10)
        file_frame.pack(fill=tk.X, pady=(0, 10))

        self.file_var = tk.StringVar()
        file_combo = ttk.Combobox(file_frame, textvariable=self.file_var,
                                 values=self.exported_files, state='readonly')
        file_combo.pack(fill=tk.X)
        file_combo.bind('<<ComboboxSelected>>', self.load_selected_file)

        # Plot area
        plot_frame = ttk.LabelFrame(main_frame, text="Visualization", padding=10)
        plot_frame.pack(fill=tk.BOTH, expand=True)

        # Create matplotlib figure
        self.fig, self.ax = plt.subplots(figsize=(10, 6))
        self.canvas = FigureCanvasTkAgg(self.fig, plot_frame)
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

        # Controls
        controls_frame = ttk.Frame(main_frame)
        controls_frame.pack(fill=tk.X, pady=(10, 0))

        ttk.Button(controls_frame, text="Line Plot",
                  command=self.plot_line).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(controls_frame, text="Scatter Plot",
                  command=self.plot_scatter).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(controls_frame, text="Histogram",
                  command=self.plot_histogram).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(controls_frame, text="Save Plot",
                  command=self.save_plot).pack(side=tk.RIGHT)

        # Load first file if available
        if self.exported_files:
            self.file_var.set(self.exported_files[0])
            self.load_selected_file()

    def load_selected_file(self, event=None):
        """Load the selected data file."""
        filename = self.file_var.get()
        if not filename:
            return

        try:
            file_path = os.path.join(self.export_directory, filename)
            self.data = pd.read_csv(file_path, comment='#')
            self.plot_line()  # Default plot
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load file: {str(e)}")

    def plot_line(self):
        """Create a line plot."""
        if not hasattr(self, 'data'):
            return

        self.ax.clear()
        numeric_cols = self.data.select_dtypes(include=[np.number]).columns

        if len(numeric_cols) >= 2:
            self.ax.plot(self.data[numeric_cols[0]], self.data[numeric_cols[1]])
            self.ax.set_xlabel(numeric_cols[0])
            self.ax.set_ylabel(numeric_cols[1])
            self.ax.set_title(f'{numeric_cols[1]} vs {numeric_cols[0]}')
        elif len(numeric_cols) == 1:
            self.ax.plot(self.data[numeric_cols[0]])
            self.ax.set_xlabel('Index')
            self.ax.set_ylabel(numeric_cols[0])
            self.ax.set_title(f'{numeric_cols[0]} vs Index')

        self.canvas.draw()

    def plot_scatter(self):
        """Create a scatter plot."""
        if not hasattr(self, 'data'):
            return

        self.ax.clear()
        numeric_cols = self.data.select_dtypes(include=[np.number]).columns

        if len(numeric_cols) >= 2:
            self.ax.scatter(self.data[numeric_cols[0]], self.data[numeric_cols[1]], alpha=0.6)
            self.ax.set_xlabel(numeric_cols[0])
            self.ax.set_ylabel(numeric_cols[1])
            self.ax.set_title(f'{numeric_cols[1]} vs {numeric_cols[0]}')

        self.canvas.draw()

    def plot_histogram(self):
        """Create a histogram."""
        if not hasattr(self, 'data'):
            return

        self.ax.clear()
        numeric_cols = self.data.select_dtypes(include=[np.number]).columns

        if len(numeric_cols) >= 1:
            self.ax.hist(self.data[numeric_cols[0]], bins=30, alpha=0.7)
            self.ax.set_xlabel(numeric_cols[0])
            self.ax.set_ylabel('Frequency')
            self.ax.set_title(f'Histogram of {numeric_cols[0]}')

        self.canvas.draw()

    def save_plot(self):
        """Save the current plot."""
        filename = filedialog.asksaveasfilename(
            defaultextension=".png",
            filetypes=[("PNG files", "*.png"), ("PDF files", "*.pdf"), ("All files", "*.*")]
        )
        if filename:
            self.fig.savefig(filename, dpi=300, bbox_inches='tight')
            messagebox.showinfo("Success", f"Plot saved to {filename}")


class DataEditorWindow:
    """Window for data processing and editing."""

    def __init__(self, parent, exported_files, export_directory):
        self.parent = parent
        self.exported_files = exported_files
        self.export_directory = export_directory
        self.window = None
        self.data = None
        self.original_data = None
        self.history = []
        self.history_index = -1

# Add this to the end of your original script:
if __name__ == "__main__":
    app = DataImportGUI()
    app.run()